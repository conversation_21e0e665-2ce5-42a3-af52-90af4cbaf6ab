
import { MenuItem } from '../context/CartContext';

export const menuCategories = [
  { id: 'starters', name: 'Starters' },
  { id: 'main', name: 'Main Course' },
  { id: 'breads', name: 'Breads' },
  { id: 'rice', name: 'Rice & Biryanis' },
  { id: 'desserts', name: '<PERSON><PERSON><PERSON>' },
  { id: 'beverages', name: 'Beverages' },
];

export const menuItems: MenuItem[] = [
  // Starters
  {
    id: 'starter-1',
    name: 'Paneer <PERSON>',
    description: 'Marinated cottage cheese cubes, grilled to perfection in a tandoor',
    price: 240,
    image: 'https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?q=80&w=500&auto=format&fit=crop',
    category: 'starters',
    isBestSeller: true,
  },
  {
    id: 'starter-2',
    name: 'Chicken 65',
    description: 'Spicy, deep-fried chicken dish originating from Chennai, India',
    price: 220,
    image: 'https://images.unsplash.com/photo-1610057099431-d73a1c9d2f2f?q=80&w=500&auto=format&fit=crop',
    category: 'starters',
    isBestSeller: true,
  },
  {
    id: 'starter-3',
    name: 'Samosa Chaat',
    description: 'Crushed samosas topped with yogurt, chutneys, and spices',
    price: 180,
    image: 'https://images.unsplash.com/photo-1601050690597-df0568f70950?q=80&w=500&auto=format&fit=crop',
    category: 'starters',
  },
  {
    id: 'starter-4',
    name: 'Hara Bhara Kebab',
    description: 'Vegetarian kebabs made with spinach, peas, and potatoes',
    price: 200,
    image: 'https://images.unsplash.com/photo-1585937421612-70a008356fbe?q=80&w=500&auto=format&fit=crop',
    category: 'starters',
  },
  {
    id: 'starter-5',
    name: 'Onion Bhaji',
    description: 'Crispy onion fritters with chickpea flour and spices',
    price: 150,
    image: 'https://images.unsplash.com/photo-1626700051175-6818013e1d4f?q=80&w=500&auto=format&fit=crop',
    category: 'starters',
  },
  
  // Main Course
  {
    id: 'main-1',
    name: 'Butter Chicken',
    description: 'Tender chicken in a rich, creamy tomato sauce',
    price: 320,
    image: 'https://images.unsplash.com/photo-1603894584373-5ac82b2ae398?q=80&w=500&auto=format&fit=crop',
    category: 'main',
    isBestSeller: true,
  },
  {
    id: 'main-2',
    name: 'Paneer Butter Masala',
    description: 'Cottage cheese cubes in a rich, creamy tomato gravy',
    price: 280,
    image: 'https://images.unsplash.com/photo-1631452180519-c014fe946bc7?q=80&w=500&auto=format&fit=crop',
    category: 'main',
    isBestSeller: true,
  },
  {
    id: 'main-3',
    name: 'Dal Makhani',
    description: 'Creamy black lentils slow-cooked with butter and spices',
    price: 180,
    image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?q=80&w=500&auto=format&fit=crop',
    category: 'main',
  },
  {
    id: 'main-4',
    name: 'Chana Masala',
    description: 'Spicy chickpea curry with tomatoes and onions',
    price: 200,
    image: 'https://images.unsplash.com/photo-1585937421612-70a008356fbe?q=80&w=500&auto=format&fit=crop',
    category: 'main',
  },
  {
    id: 'main-5',
    name: 'Malai Kofta',
    description: 'Fried vegetable dumplings in a creamy sauce',
    price: 260,
    image: 'https://images.unsplash.com/photo-1575451527104-9fc895bd86dc?q=80&w=500&auto=format&fit=crop',
    category: 'main',
  },
  
  // Breads
  {
    id: 'bread-1',
    name: 'Butter Naan',
    description: 'Soft leavened bread brushed with butter',
    price: 60,
    image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?q=80&w=500&auto=format&fit=crop',
    category: 'breads',
    isBestSeller: true,
  },
  {
    id: 'bread-2',
    name: 'Garlic Naan',
    description: 'Naan bread topped with garlic and herbs',
    price: 70,
    image: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?q=80&w=500&auto=format&fit=crop',
    category: 'breads',
  },
  {
    id: 'bread-3',
    name: 'Tandoori Roti',
    description: 'Whole wheat flatbread baked in a tandoor',
    price: 45,
    image: 'https://images.unsplash.com/photo-1574653191046-2bb4c0838e78?q=80&w=500&auto=format&fit=crop',
    category: 'breads',
  },
  {
    id: 'bread-4',
    name: 'Lachha Paratha',
    description: 'Multi-layered whole wheat flatbread',
    price: 80,
    image: 'https://images.unsplash.com/photo-1571167530149-c1105d4d0de9?q=80&w=500&auto=format&fit=crop',
    category: 'breads',
  },
  
  // Rice & Biryanis
  {
    id: 'rice-1',
    name: 'Veg Biryani',
    description: 'Fragrant rice cooked with vegetables and aromatic spices',
    price: 200,
    image: 'https://images.unsplash.com/photo-1563379091339-03b21ab4a4f8?q=80&w=500&auto=format&fit=crop',
    category: 'rice',
  },
  {
    id: 'rice-2',
    name: 'Hyderabadi Chicken Biryani',
    description: 'Aromatic rice cooked with chicken in authentic Hyderabadi style',
    price: 280,
    image: 'https://images.unsplash.com/photo-1589302168068-964664d93dc0?q=80&w=500&auto=format&fit=crop',
    category: 'rice',
    isBestSeller: true,
  },
  {
    id: 'rice-3',
    name: 'Jeera Rice',
    description: 'Basmati rice tempered with cumin seeds',
    price: 150,
    image: 'https://images.unsplash.com/photo-1516684669134-de6f7c473a2a?q=80&w=500&auto=format&fit=crop',
    category: 'rice',
  },
  {
    id: 'rice-4',
    name: 'Vegetable Pulao',
    description: 'Rice cooked with mixed vegetables and whole spices',
    price: 180,
    image: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?q=80&w=500&auto=format&fit=crop',
    category: 'rice',
  },
  
  // Desserts
  {
    id: 'dessert-1',
    name: 'Gulab Jamun',
    description: 'Deep-fried milk solids soaked in sugar syrup',
    price: 100,
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?q=80&w=500&auto=format&fit=crop',
    category: 'desserts',
    isBestSeller: true,
  },
  {
    id: 'dessert-2',
    name: 'Rasmalai',
    description: 'Soft cottage cheese dumplings in sweetened, cardamom-flavored milk',
    price: 120,
    image: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?q=80&w=500&auto=format&fit=crop',
    category: 'desserts',
  },
  {
    id: 'dessert-3',
    name: 'Kheer',
    description: 'Rice pudding made with milk, sugar, and nuts',
    price: 110,
    image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?q=80&w=500&auto=format&fit=crop',
    category: 'desserts',
  },
  {
    id: 'dessert-4',
    name: 'Jalebi',
    description: 'Deep-fried batter soaked in sugar syrup',
    price: 90,
    image: 'https://images.unsplash.com/photo-1606471191009-63a5b5807ec2?q=80&w=500&auto=format&fit=crop',
    category: 'desserts',
  },
  
  // Beverages
  {
    id: 'beverage-1',
    name: 'Masala Chai',
    description: 'Spiced Indian tea with milk',
    price: 60,
    image: 'https://images.unsplash.com/photo-1571934811356-5cc061b6821f?q=80&w=500&auto=format&fit=crop',
    category: 'beverages',
    isBestSeller: true,
  },
  {
    id: 'beverage-2',
    name: 'Mango Lassi',
    description: 'Refreshing yogurt-based drink with mango pulp',
    price: 120,
    image: 'https://images.unsplash.com/photo-1553787499-6d39d5d4a3c1?q=80&w=500&auto=format&fit=crop',
    category: 'beverages',
  },
  {
    id: 'beverage-3',
    name: 'Filter Coffee',
    description: 'South Indian style coffee with frothy milk',
    price: 80,
    image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?q=80&w=500&auto=format&fit=crop',
    category: 'beverages',
  },
  {
    id: 'beverage-4',
    name: 'Nimbu Pani',
    description: 'Refreshing lemonade with Indian spices',
    price: 70,
    image: 'https://images.unsplash.com/photo-1587888637140-849b25d80ef9?q=80&w=500&auto=format&fit=crop',
    category: 'beverages',
  },
];

export const getMenuItemsByCategory = (category: string) => {
  return menuItems.filter(item => item.category === category);
};
