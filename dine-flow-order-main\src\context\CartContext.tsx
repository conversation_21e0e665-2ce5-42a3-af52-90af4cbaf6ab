import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  isBestSeller?: boolean;
}

export interface CartItem extends MenuItem {
  quantity: number;
}

export interface UserInfo {
  name: string;
  phone: string;
  email?: string;
}

interface CartContextType {
  items: CartItem[];
  tableNumber: number | null;
  addToCart: (item: MenuItem) => void;
  removeFromCart: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  setTableNumber: (number: number) => void;
  getTotalItems: () => number;
  getSubtotal: () => number;
  tipPercentage: number;
  setTipPercentage: (percentage: number) => void;
  getTipAmount: () => number;
  getTotal: () => number;
  userInfo: UserInfo | null;
  setUserInfo: (userInfo: UserInfo | null) => void;
  isUserRegistered: boolean;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([]);
  const [tableNumber, setTableNumber] = useState<number | null>(null);
  const [tipPercentage, setTipPercentage] = useState(0);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(() => {
    const saved = localStorage.getItem('dineflow-user');
    return saved ? JSON.parse(saved) : null;
  });

  useEffect(() => {
    if (userInfo) {
      localStorage.setItem('dineflow-user', JSON.stringify(userInfo));
    } else {
      localStorage.removeItem('dineflow-user');
    }
  }, [userInfo]);

  const isUserRegistered = userInfo !== null;

  const addToCart = (item: MenuItem) => {
    setItems((prevItems) => {
      const existingItem = prevItems.find((i) => i.id === item.id);
      if (existingItem) {
        return prevItems.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        );
      }
      return [...prevItems, { ...item, quantity: 1 }];
    });
  };

  const removeFromCart = (itemId: string) => {
    setItems((prevItems) => prevItems.filter((i) => i.id !== itemId));
  };

  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }
    setItems((prevItems) =>
      prevItems.map((i) => (i.id === itemId ? { ...i, quantity } : i))
    );
  };

  const clearCart = () => {
    setItems([]);
  };

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0);
  };

  const getSubtotal = () => {
    return items.reduce((total, item) => total + item.price * item.quantity, 0);
  };

  const getTipAmount = () => {
    return getSubtotal() * (tipPercentage / 100);
  };

  const getTotal = () => {
    return getSubtotal() + getTipAmount();
  };

  return (
    <CartContext.Provider
      value={{
        items,
        tableNumber,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        setTableNumber,
        getTotalItems,
        getSubtotal,
        tipPercentage,
        setTipPercentage,
        getTipAmount,
        getTotal,
        userInfo,
        setUserInfo,
        isUserRegistered,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
