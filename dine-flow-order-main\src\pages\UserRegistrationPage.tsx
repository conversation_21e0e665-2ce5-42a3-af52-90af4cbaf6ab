
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { useNavigate } from 'react-router-dom';
import { useCart } from '@/context/CartContext';
import { toast } from 'sonner';
import { User, Phone, Mail, Shield, Sparkles, ChefHat } from 'lucide-react';
import { RecaptchaVerifier, signInWithPhoneNumber, ConfirmationResult } from 'firebase/auth';
import { auth } from '@/lib/firebase';

declare global {
  interface Window {
    recaptchaVerifier?: RecaptchaVerifier;
    confirmationResult?: ConfirmationResult;
  }
}

const UserRegistrationPage: React.FC = () => {
  const [step, setStep] = useState<'details' | 'otp'>('details');
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { setUserInfo } = useCart();

  const setupRecaptcha = () => {
    // Clear existing verifier if it exists
    if (window.recaptchaVerifier) {
      try {
        window.recaptchaVerifier.clear();
        window.recaptchaVerifier = undefined;
      } catch (error) {
        console.log('Error clearing recaptcha:', error);
      }
    }

    // Create new verifier
    try {
      window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved');
        },
        'expired-callback': () => {
          console.log('reCAPTCHA expired');
          // Clear and recreate on expiry
          if (window.recaptchaVerifier) {
            window.recaptchaVerifier.clear();
            window.recaptchaVerifier = undefined;
          }
        }
      });
    } catch (error) {
      console.error('Error setting up reCAPTCHA:', error);
      throw error;
    }
  };

  const handleDetailsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim() || !phone.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (phone.length < 10) {
      toast.error('Please enter a valid phone number');
      return;
    }

    setIsLoading(true);
    
    try {
      // Format phone number with country code if not already present
      const formattedPhone = phone.startsWith('+') ? phone : `+91${phone}`;
      
      // Store guest info temporarily
      sessionStorage.setItem('guestName', name.trim());
      sessionStorage.setItem('guestEmail', email.trim());
      sessionStorage.setItem('guestPhone', formattedPhone);
      
      // Setup reCAPTCHA
      setupRecaptcha();
      
      // Send OTP
      const confirmationResult = await signInWithPhoneNumber(auth, formattedPhone, window.recaptchaVerifier!);
      window.confirmationResult = confirmationResult;
      
      setIsLoading(false);
      setStep('otp');
      toast.success(`OTP sent to ${formattedPhone}`);
    } catch (error: any) {
      setIsLoading(false);
      console.error('Error sending OTP:', error);
      
      // Clear reCAPTCHA on error
      if (window.recaptchaVerifier) {
        try {
          window.recaptchaVerifier.clear();
          window.recaptchaVerifier = undefined;
        } catch (clearError) {
          console.log('Error clearing recaptcha after failure:', clearError);
        }
      }
      
      // Handle specific Firebase errors
      if (error.code === 'auth/billing-not-enabled') {
        toast.error('Firebase billing not enabled. Please check your Firebase project configuration.');
      } else if (error.code === 'auth/too-many-requests') {
        toast.error('Too many requests. Please try again later.');
      } else {
        toast.error(`Failed to send OTP: ${error.message}`);
      }
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (otp.length !== 6) {
      toast.error('Please enter the complete OTP');
      return;
    }

    if (!window.confirmationResult) {
      toast.error('Please request OTP first');
      return;
    }

    setIsLoading(true);
    
    try {
      // Verify OTP
      await window.confirmationResult.confirm(otp);
      
      // Get stored info
      const storedName = sessionStorage.getItem('guestName') || '';
      const storedEmail = sessionStorage.getItem('guestEmail') || '';
      const storedPhone = sessionStorage.getItem('guestPhone') || '';
      
      // Store user info in context
      setUserInfo({
        name: storedName,
        phone: storedPhone,
        email: storedEmail || undefined
      });
      
      // Mark as verified
      sessionStorage.setItem('isVerified', 'true');
      
      setIsLoading(false);
      toast.success('Phone verified! Welcome to Food Hall');
      navigate('/');
    } catch (error: any) {
      setIsLoading(false);
      console.error('Error verifying OTP:', error);
      toast.error('Invalid OTP, please try again.');
    }
  };

  const resendOtp = async () => {
    const storedPhone = sessionStorage.getItem('guestPhone');
    if (!storedPhone) {
      toast.error('Phone number not found');
      return;
    }

    setIsLoading(true);
    
    try {
      // Setup new reCAPTCHA
      setupRecaptcha();
      
      // Resend OTP
      const confirmationResult = await signInWithPhoneNumber(auth, storedPhone, window.recaptchaVerifier!);
      window.confirmationResult = confirmationResult;
      
      setIsLoading(false);
      toast.success(`OTP resent to ${storedPhone}`);
    } catch (error: any) {
      setIsLoading(false);
      console.error('Error resending OTP:', error);
      
      // Clear reCAPTCHA on error
      if (window.recaptchaVerifier) {
        try {
          window.recaptchaVerifier.clear();
          window.recaptchaVerifier = undefined;
        } catch (clearError) {
          console.log('Error clearing recaptcha after resend failure:', clearError);
        }
      }
      
      if (error.code === 'auth/billing-not-enabled') {
        toast.error('Firebase billing not enabled. Please check your Firebase project configuration.');
      } else {
        toast.error(`Failed to resend OTP: ${error.message}`);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50 pattern-dots flex items-center justify-center p-4">
      {/* Floating decorative elements */}
      <div className="fixed top-10 left-10 text-orange-300 animate-float">
        <ChefHat size={48} />
      </div>
      <div className="fixed top-20 right-20 text-pink-300 animate-float-delayed">
        <Sparkles size={32} />
      </div>
      <div className="fixed bottom-20 left-20 text-purple-300 animate-float">
        <User size={40} />
      </div>

      <Card className="w-full max-w-md shadow-2xl border-0 bg-white/95 backdrop-blur-sm glow-orange animate-scale-in">
        <CardHeader className="text-center pb-6">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center mb-4 animate-pulse-gentle">
            {step === 'details' ? (
              <User className="text-white" size={32} />
            ) : (
              <Shield className="text-white" size={32} />
            )}
          </div>
          <CardTitle className="text-2xl font-bold gradient-text">
            {step === 'details' ? 'Welcome to Food Hall' : 'Verify Your Phone'}
          </CardTitle>
          <p className="text-gray-600 mt-2">
            {step === 'details' 
              ? 'Please provide your details to continue' 
              : `Enter the OTP sent to ${sessionStorage.getItem('guestPhone') || phone}`
            }
          </p>
        </CardHeader>

        <CardContent>
          {step === 'details' ? (
            <form onSubmit={handleDetailsSubmit} className="space-y-6">
              <div className="space-y-2 animate-slide-up-fade stagger-1">
                <Label htmlFor="name" className="text-sm font-medium flex items-center gap-2">
                  <User size={16} className="text-orange-500" />
                  Full Name *
                </Label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Enter your full name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="border-orange-200 focus:border-orange-500 transition-colors"
                  required
                />
              </div>

              <div className="space-y-2 animate-slide-up-fade stagger-2">
                <Label htmlFor="phone" className="text-sm font-medium flex items-center gap-2">
                  <Phone size={16} className="text-orange-500" />
                  Phone Number *
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="Enter your phone number (without +91)"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value.replace(/\D/g, ''))}
                  className="border-orange-200 focus:border-orange-500 transition-colors"
                  maxLength={10}
                  required
                />
              </div>

              <div className="space-y-2 animate-slide-up-fade stagger-3">
                <Label htmlFor="email" className="text-sm font-medium flex items-center gap-2">
                  <Mail size={16} className="text-orange-500" />
                  Email Address (Optional)
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email (optional)"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="border-orange-200 focus:border-orange-500 transition-colors"
                />
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full btn-primary animate-slide-up-fade stagger-4"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Sending OTP...
                  </div>
                ) : (
                  'Send OTP'
                )}
              </Button>

              {/* Invisible reCAPTCHA container */}
              <div id="recaptcha-container"></div>
            </form>
          ) : (
            <form onSubmit={handleOtpSubmit} className="space-y-6">
              <div className="space-y-4 animate-slide-up-fade">
                <Label className="text-sm font-medium text-center block">
                  Enter 6-digit OTP
                </Label>
                <div className="flex justify-center">
                  <InputOTP
                    maxLength={6}
                    value={otp}
                    onChange={setOtp}
                  >
                    <InputOTPGroup>
                      <InputOTPSlot index={0} className="border-orange-200" />
                      <InputOTPSlot index={1} className="border-orange-200" />
                      <InputOTPSlot index={2} className="border-orange-200" />
                      <InputOTPSlot index={3} className="border-orange-200" />
                      <InputOTPSlot index={4} className="border-orange-200" />
                      <InputOTPSlot index={5} className="border-orange-200" />
                    </InputOTPGroup>
                  </InputOTP>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resendOtp}
                  disabled={isLoading}
                  className="flex-1 border-orange-200 text-orange-600 hover:bg-orange-50"
                >
                  {isLoading ? 'Sending...' : 'Resend OTP'}
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || otp.length !== 6}
                  className="flex-1 btn-primary"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Verifying...
                    </div>
                  ) : (
                    'Verify & Continue'
                  )}
                </Button>
              </div>

              <Button
                type="button"
                variant="ghost"
                onClick={() => setStep('details')}
                className="w-full text-gray-500 hover:text-gray-700"
              >
                ← Back to Details
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UserRegistrationPage;
