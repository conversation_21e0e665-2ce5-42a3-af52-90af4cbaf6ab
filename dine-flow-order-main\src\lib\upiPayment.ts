// UPI Payment Integration for Food Hall Express
// Supports Google Pay, PhonePe, Paytm, BHIM UPI, and other UPI apps

export interface UPIPaymentDetails {
  merchantName: string;
  merchantUPI: string; // Your UPI ID (e.g., yourname@paytm, yourname@googlepay)
  amount: number;
  transactionNote: string;
  transactionRef: string;
  currency: string;
}

export interface UPIProvider {
  id: string;
  name: string;
  icon: string;
  packageName: string; // Android package name for deep linking
  iosScheme: string; // iOS URL scheme
}

// UPI Providers with their deep link configurations
export const UPI_PROVIDERS: UPIProvider[] = [
  {
    id: 'googlepay',
    name: 'Google Pay',
    icon: 'https://cdn-icons-png.flaticon.com/512/6124/6124998.png',
    packageName: 'com.google.android.apps.nbu.paisa.user',
    iosScheme: 'gpay'
  },
  {
    id: 'phonepe',
    name: 'PhonePe',
    icon: 'https://cdn-icons-png.flaticon.com/512/6124/6124997.png',
    packageName: 'com.phonepe.app',
    iosScheme: 'phonepe'
  },
  {
    id: 'paytm',
    name: 'Paytm',
    icon: 'https://cdn-icons-png.flaticon.com/512/6124/6124999.png',
    packageName: 'net.one97.paytm',
    iosScheme: 'paytm'
  },
  {
    id: 'bhim',
    name: 'BHIM UPI',
    icon: 'https://cdn-icons-png.flaticon.com/512/10268/10268684.png',
    packageName: 'in.org.npci.upiapp',
    iosScheme: 'bhim'
  },
  {
    id: 'amazonpay',
    name: 'Amazon Pay',
    icon: 'https://cdn-icons-png.flaticon.com/512/5968/5968313.png',
    packageName: 'in.amazon.mShop.android.shopping',
    iosScheme: 'amazonpay'
  }
];

// Your restaurant's UPI configuration
// ✅ CONFIGURED: Using your actual UPI ID
export const RESTAURANT_UPI_CONFIG = {
  merchantName: 'Food Hall Express',
  merchantUPI: 'yashgladiator01@oksbi', // ✅ Your SBI UPI ID
  // Alternative UPI IDs for fallback
  fallbackUPIs: [
    'yashgladiator01@oksbi',   // Primary SBI UPI ID
    '**********@paytm',        // Phone-based Paytm UPI
    '**********@googlepay',    // Phone-based Google Pay UPI
    'foodhallexpress@paytm'    // Business UPI ID (if available)
  ]
};

// Generate UPI payment URL
export const generateUPIPaymentURL = (
  paymentDetails: UPIPaymentDetails,
  provider?: UPIProvider
): string => {
  const {
    merchantName,
    merchantUPI,
    amount,
    transactionNote,
    transactionRef,
    currency = 'INR'
  } = paymentDetails;

  // Standard UPI URL format
  const baseUPIURL = `upi://pay?pa=${encodeURIComponent(merchantUPI)}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=${currency}&tn=${encodeURIComponent(transactionNote)}&tr=${encodeURIComponent(transactionRef)}`;

  // Provider-specific deep links
  if (provider) {
    switch (provider.id) {
      case 'googlepay':
        return `gpay://upi/pay?pa=${encodeURIComponent(merchantUPI)}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=${currency}&tn=${encodeURIComponent(transactionNote)}&tr=${encodeURIComponent(transactionRef)}`;
      
      case 'phonepe':
        return `phonepe://pay?pa=${encodeURIComponent(merchantUPI)}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=${currency}&tn=${encodeURIComponent(transactionNote)}&tr=${encodeURIComponent(transactionRef)}`;
      
      case 'paytm':
        return `paytmmp://pay?pa=${encodeURIComponent(merchantUPI)}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=${currency}&tn=${encodeURIComponent(transactionNote)}&tr=${encodeURIComponent(transactionRef)}`;
      
      case 'bhim':
        return `bhim://pay?pa=${encodeURIComponent(merchantUPI)}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=${currency}&tn=${encodeURIComponent(transactionNote)}&tr=${encodeURIComponent(transactionRef)}`;
      
      default:
        return baseUPIURL;
    }
  }

  return baseUPIURL;
};

// Generate QR code data for UPI payment
export const generateUPIQRData = (paymentDetails: UPIPaymentDetails): string => {
  return generateUPIPaymentURL(paymentDetails);
};

// Initiate UPI payment
export const initiateUPIPayment = async (
  paymentDetails: UPIPaymentDetails,
  provider: UPIProvider
): Promise<{ success: boolean; paymentURL?: string; error?: string }> => {
  try {
    const paymentURL = generateUPIPaymentURL(paymentDetails, provider);
    
    console.log('🔄 Initiating UPI payment:', {
      provider: provider.name,
      amount: paymentDetails.amount,
      merchant: paymentDetails.merchantUPI,
      reference: paymentDetails.transactionRef
    });

    // Try to open the UPI app
    const opened = await openUPIApp(paymentURL, provider);
    
    if (opened) {
      return {
        success: true,
        paymentURL
      };
    } else {
      // Fallback to generic UPI URL
      const genericURL = generateUPIPaymentURL(paymentDetails);
      window.open(genericURL, '_blank');
      
      return {
        success: true,
        paymentURL: genericURL
      };
    }
  } catch (error) {
    console.error('UPI payment initiation failed:', error);
    return {
      success: false,
      error: 'Failed to initiate UPI payment. Please try again.'
    };
  }
};

// Open specific UPI app
const openUPIApp = async (paymentURL: string, provider: UPIProvider): Promise<boolean> => {
  try {
    // For mobile devices, try app-specific deep links
    if (isMobileDevice()) {
      // Try to open the specific app
      window.location.href = paymentURL;
      
      // Wait a bit and check if the app opened
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // If we're still here, the app might not be installed
      // Fall back to generic UPI
      if (document.visibilityState === 'visible') {
        console.log(`${provider.name} app not found, trying generic UPI`);
        return false;
      }
      
      return true;
    } else {
      // For desktop, open in new tab
      window.open(paymentURL, '_blank');
      return true;
    }
  } catch (error) {
    console.error('Failed to open UPI app:', error);
    return false;
  }
};

// Check if device is mobile
const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Generate unique transaction reference
export const generateTransactionRef = (orderPrefix: string = 'FHE'): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `${orderPrefix}${timestamp}${random}`.toUpperCase();
};

// Validate UPI ID format
export const validateUPIId = (upiId: string): boolean => {
  const upiRegex = /^[a-zA-Z0-9.\-_]{2,256}@[a-zA-Z]{2,64}$/;
  return upiRegex.test(upiId);
};

// Format amount for UPI (remove decimals if whole number)
export const formatUPIAmount = (amount: number): string => {
  return amount % 1 === 0 ? amount.toString() : amount.toFixed(2);
};

// Check UPI app availability
export const checkUPIAppAvailability = async (provider: UPIProvider): Promise<boolean> => {
  if (!isMobileDevice()) return true; // Assume available on desktop
  
  try {
    // This is a basic check - in a real app, you might want more sophisticated detection
    const testURL = `${provider.iosScheme}://test`;
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = testURL;
    document.body.appendChild(iframe);
    
    setTimeout(() => {
      document.body.removeChild(iframe);
    }, 100);
    
    return true; // Assume available for now
  } catch (error) {
    return false;
  }
};

// Payment verification (manual process)
export const createPaymentVerificationInstructions = (
  transactionRef: string,
  amount: number
): string => {
  return `
Payment Instructions:
1. Complete the UPI payment in your app
2. Note the transaction ID from your payment app
3. Return to this page and confirm payment
4. Reference: ${transactionRef}
5. Amount: ₹${amount}

If payment fails, please try again or contact support.
  `.trim();
};
