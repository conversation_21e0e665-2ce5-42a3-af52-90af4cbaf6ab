
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
 
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Format date to a readable string
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  }).format(date);
}

// Format currency to Indian Rupee format
export function formatCurrency(amount: number): string {
  return `₹${amount.toFixed(0)}`;
}

// Get initials from a name
export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase();
}

// Truncate text with ellipsis
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

// Check if a dish is vegetarian (mock implementation)
export function isVegetarian(itemId: string): boolean {
  // This is a mock implementation - in a real app, this would come from your database
  const vegDishIds = [
    'starter-1', 'starter-3', 'starter-4', 
    'main-2', 'main-3', 'main-4', 'main-5', 
    'dessert-1', 'dessert-2', 'dessert-3', 'dessert-4'
  ];
  return vegDishIds.includes(itemId);
}

// Check if a dish is spicy (mock implementation)
export function isSpicy(itemId: string): boolean {
  // This is a mock implementation - in a real app, this would come from your database
  const spicyDishIds = [
    'starter-2', 'starter-5', 
    'main-1', 'main-6', 
    'biryani-1', 'biryani-2'
  ];
  return spicyDishIds.includes(itemId);
}
