
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyDKRzzeWqULyaBoovxdOR2ucjJQr1JX0YY",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "otp-auth-f9b98.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "otp-auth-f9b98",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "otp-auth-f9b98.appspot.com",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "10968363497",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:10968363497:web:2aea80d58a01d8d858db78",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-ED4ZYNPS72"
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export default app;
