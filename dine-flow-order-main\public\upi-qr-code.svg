<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
  <!-- QR Code Pattern for yashgladiator01@oksbi -->
  <!-- This is a simplified representation - you should replace with actual QR code data -->
  <rect width="300" height="300" fill="white"/>
  
  <!-- QR Code Corner Markers -->
  <rect x="20" y="20" width="60" height="60" fill="black"/>
  <rect x="30" y="30" width="40" height="40" fill="white"/>
  <rect x="40" y="40" width="20" height="20" fill="black"/>
  
  <rect x="220" y="20" width="60" height="60" fill="black"/>
  <rect x="230" y="30" width="40" height="40" fill="white"/>
  <rect x="240" y="40" width="20" height="20" fill="black"/>
  
  <rect x="20" y="220" width="60" height="60" fill="black"/>
  <rect x="30" y="230" width="40" height="40" fill="white"/>
  <rect x="40" y="240" width="20" height="20" fill="black"/>
  
  <!-- QR Code Data Pattern (simplified) -->
  <rect x="100" y="20" width="10" height="10" fill="black"/>
  <rect x="120" y="20" width="10" height="10" fill="black"/>
  <rect x="140" y="20" width="10" height="10" fill="black"/>
  <rect x="160" y="20" width="10" height="10" fill="black"/>
  <rect x="180" y="20" width="10" height="10" fill="black"/>
  
  <!-- Add more QR pattern elements as needed -->
  <!-- This is a placeholder - the actual QR code should be generated from the UPI data -->
  
  <!-- UPI Logo in center -->
  <circle cx="150" cy="150" r="25" fill="white" stroke="black" stroke-width="2"/>
  <text x="150" y="155" text-anchor="middle" font-family="Arial" font-size="12" fill="black">UPI</text>
</svg>
