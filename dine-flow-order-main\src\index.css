
@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 220 13% 18%;
    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;
    --primary: 14 100% 57%;
    --primary-foreground: 0 0% 98%;
    --secondary: 271 81% 56%;
    --secondary-foreground: 0 0% 98%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 47 100% 95%;
    --accent-foreground: 220 13% 18%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 14 100% 57%;
    --radius: 12px;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-poppins font-semibold;
  }

  h1 {
    @apply text-3xl md:text-5xl;
  }

  h2 {
    @apply text-2xl md:text-4xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }
}

@layer components {
  .menu-card {
    @apply relative bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] border border-orange-100;
  }

  .menu-card::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-br from-orange-50/50 to-pink-50/50 opacity-0 transition-opacity duration-500;
  }

  .menu-card:hover::before {
    @apply opacity-100;
  }

  .menu-image {
    @apply w-full h-40 object-cover transition-all duration-500;
  }

  .menu-card:hover .menu-image {
    @apply scale-110 brightness-110;
  }

  .best-seller-tag {
    @apply absolute top-3 right-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse-gentle;
  }

  .veg-tag {
    @apply absolute top-3 left-3 bg-gradient-to-r from-green-500 to-green-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg;
  }

  .spicy-tag {
    @apply absolute top-3 left-3 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg;
  }

  .add-to-cart-btn {
    @apply bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full w-10 h-10 flex items-center justify-center shadow-lg hover:shadow-xl hover:scale-110 transition-all duration-300 hover:rotate-6;
  }

  .quantity-control {
    @apply flex items-center space-x-2 border border-orange-200 rounded-xl overflow-hidden bg-orange-50 shadow-md;
  }

  .quantity-btn {
    @apply bg-white text-orange-600 w-8 h-8 flex items-center justify-center hover:bg-orange-100 transition-all duration-300 font-semibold hover:scale-110;
  }

  /* Enhanced hero section */
  .hero-banner {
    @apply relative overflow-hidden rounded-3xl mb-8 bg-gradient-to-br from-orange-400 via-red-400 to-pink-400 shadow-2xl;
    height: 300px;
  }

  .hero-banner::before {
    @apply content-[''] absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.2)_0%,transparent_50%)] animate-pulse-gentle;
  }

  .hero-overlay {
    @apply absolute inset-0 bg-gradient-to-br from-black/30 to-black/10;
  }

  .hero-content {
    @apply relative flex flex-col items-center justify-center h-full text-center text-white p-8 z-10;
  }

  /* Enhanced button styles */
  .btn-primary {
    @apply bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl px-6 py-3 font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-orange-200;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl px-6 py-3 font-semibold hover:from-purple-600 hover:to-purple-700 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-200;
  }

  .btn-outline {
    @apply border-2 border-orange-500 text-orange-600 bg-transparent rounded-xl px-6 py-3 font-semibold hover:bg-orange-50 transition-all duration-300 hover:scale-105 hover:shadow-md;
  }

  /* Enhanced filter pills */
  .filter-pill {
    @apply text-sm px-6 py-3 rounded-full border-2 border-orange-200 font-semibold transition-all duration-300 hover:scale-105 bg-white hover:border-orange-400 hover:shadow-lg;
  }

  .filter-pill-active {
    @apply bg-gradient-to-r from-orange-500 to-orange-600 text-white border-orange-500 shadow-lg scale-105;
  }

  /* Loading skeleton with shimmer */
  .skeleton {
    @apply bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer rounded-xl;
  }

  /* Floating elements */
  .floating-element {
    @apply animate-float;
  }

  .floating-delayed {
    @apply animate-float-delayed;
  }

  /* Interactive icons */
  .interactive-icon {
    @apply transition-all duration-300 hover:scale-125 hover:rotate-12 cursor-pointer;
  }

  /* Card hover effects */
  .hover-lift {
    @apply transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:-translate-y-2;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 bg-clip-text text-transparent;
  }

  /* Animated borders */
  .animated-border {
    @apply relative;
  }

  .animated-border::before {
    @apply content-[''] absolute inset-0 rounded-xl bg-gradient-to-r from-orange-500 via-pink-500 to-purple-500 p-[2px] opacity-0 transition-opacity duration-300;
  }

  .animated-border:hover::before {
    @apply opacity-100;
  }

  .animated-border > * {
    @apply relative z-10 bg-white rounded-xl;
  }

  /* Enhanced navigation */
  .nav-item {
    @apply relative transition-all duration-300 hover:scale-110;
  }

  .nav-item::after {
    @apply content-[''] absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-orange-500 to-pink-500 transition-all duration-300;
  }

  .nav-item:hover::after {
    @apply w-full;
  }
}

/* Enhanced animations */
@layer utilities {
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(2deg); }
    66% { transform: translateY(5px) rotate(-2deg); }
  }

  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(8px) rotate(-1deg); }
    66% { transform: translateY(-12px) rotate(1deg); }
  }

  @keyframes pulse-gentle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  @keyframes bounce-gentle {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
  }

  @keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(3deg); }
    75% { transform: rotate(-3deg); }
  }

  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  @keyframes slide-up-fade {
    0% {
      opacity: 0;
      transform: translateY(30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slide-in-left {
    0% {
      opacity: 0;
      transform: translateX(-30px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scale-in {
    0% {
      opacity: 0;
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
  }

  .animate-pulse-gentle {
    animation: pulse-gentle 3s ease-in-out infinite;
  }

  .animate-bounce-gentle {
    animation: bounce-gentle 2s ease-in-out infinite;
  }

  .animate-wiggle {
    animation: wiggle 0.5s ease-in-out;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .animate-slide-up-fade {
    animation: slide-up-fade 0.6s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slide-in-left 0.8s ease-out forwards;
  }

  .animate-scale-in {
    animation: scale-in 0.4s ease-out forwards;
  }

  /* Stagger animations */
  .stagger-1 { animation-delay: 0.1s; }
  .stagger-2 { animation-delay: 0.2s; }
  .stagger-3 { animation-delay: 0.3s; }
  .stagger-4 { animation-delay: 0.4s; }
  .stagger-5 { animation-delay: 0.5s; }
}

/* Background patterns */
.pattern-dots {
  background-image: radial-gradient(circle, rgba(255, 165, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.pattern-grid {
  background-image: linear-gradient(rgba(255, 165, 0, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 165, 0, 0.1) 1px, transparent 1px);
  background-size: 30px 30px;
}

/* Glowing effects */
.glow-orange {
  box-shadow: 0 0 20px rgba(255, 165, 0, 0.3);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

/* Toast enhancement */
.toast-container {
  @apply fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50;
}

.toast {
  @apply bg-white shadow-2xl rounded-xl p-4 animate-slide-up-fade border border-orange-200 glow-orange;
}
