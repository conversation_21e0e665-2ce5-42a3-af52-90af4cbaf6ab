import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { QrCode, Copy, Download } from 'lucide-react';
import { RESTAURANT_UPI_CONFIG } from '@/lib/upiPayment';
import { toast } from '@/components/ui/use-toast';

interface UPIQRCodeProps {
  amount: number;
  transactionRef: string;
  tableNumber: string;
}

const UPIQRCode: React.FC<UPIQRCodeProps> = ({ amount, transactionRef, tableNumber }) => {

  // Generate UPI payment URL with your UPI ID and dynamic amount
  const generateUPIPaymentURL = () => {
    return `upi://pay?pa=${RESTAURANT_UPI_CONFIG.merchantUPI}&pn=${encodeURIComponent(RESTAURANT_UPI_CONFIG.merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(`Food Hall Express - Table ${tableNumber} - Order ${transactionRef}`)}`;
  };

  // Generate QR code URL using QR Server API with your UPI payment data
  const getQRCodeURL = () => {
    const upiPaymentURL = generateUPIPaymentURL();
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(upiPaymentURL)}&bgcolor=ffffff&color=000000&qzone=1&format=png`;
  };

  const copyUPIId = () => {
    navigator.clipboard.writeText(RESTAURANT_UPI_CONFIG.merchantUPI);
    toast({
      title: "Copied!",
      description: "UPI ID copied to clipboard",
      duration: 2000,
    });
  };

  const copyQRData = () => {
    const upiLink = generateUPIPaymentURL();
    navigator.clipboard.writeText(upiLink);
    toast({
      title: "Copied!",
      description: "Payment link copied to clipboard",
      duration: 2000,
    });
  };

  const downloadQRCode = () => {
    // Create a temporary link to download the QR code
    const link = document.createElement('a');
    link.download = `upi-qr-${transactionRef}.png`;
    link.href = getQRCodeURL();
    link.click();
  };

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-800">
          <QrCode className="h-5 w-5" />
          Scan to Pay with Any UPI App
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center space-y-4">
          {/* Your UPI QR Code */}
          <div className="bg-white p-4 rounded-lg border-2 border-blue-200">
            <img
              src={getQRCodeURL()}
              alt={`UPI QR Code for ${RESTAURANT_UPI_CONFIG.merchantUPI} - Amount: ₹${amount}`}
              className="block w-48 h-48"
              style={{ maxWidth: '200px', maxHeight: '200px' }}
              onError={(e) => {
                // Fallback if QR code generation fails
                e.currentTarget.src = `data:image/svg+xml;base64,${btoa(`
                  <svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <rect width="200" height="200" fill="#f0f0f0"/>
                    <text x="100" y="90" text-anchor="middle" font-family="Arial" font-size="14" fill="#333">QR Code</text>
                    <text x="100" y="110" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Scan to Pay</text>
                    <text x="100" y="130" text-anchor="middle" font-family="Arial" font-size="16" fill="#000">₹${amount}</text>
                    <text x="100" y="150" text-anchor="middle" font-family="Arial" font-size="10" fill="#666">${RESTAURANT_UPI_CONFIG.merchantUPI}</text>
                  </svg>
                `)}`;
              }}
            />
          </div>

          {/* UPI ID Display */}
          <div className="text-center">
            <p className="text-sm font-medium text-blue-800">UPI ID</p>
            <p className="text-lg font-mono font-bold text-blue-900">{RESTAURANT_UPI_CONFIG.merchantUPI}</p>
            <p className="text-xs text-blue-600 mt-1">Scan with any UPI app to pay ₹{amount}</p>
          </div>

          {/* Payment Details */}
          <div className="w-full space-y-3">
            <div className="flex items-center justify-between p-3 bg-white rounded border">
              <div>
                <p className="text-sm font-medium">Amount to Pay</p>
                <p className="text-xl font-bold text-primary">₹{amount}</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">Pay to</p>
                <p className="text-xs text-gray-600 font-mono">{RESTAURANT_UPI_CONFIG.merchantUPI}</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-white rounded border">
              <div>
                <p className="text-sm font-medium">Transaction Reference</p>
                <p className="text-xs text-gray-600 font-mono">{transactionRef}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={copyQRData}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              className="flex-1"
              onClick={copyUPIId}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy UPI ID
            </Button>
            <Button
              variant="outline"
              className="flex-1"
              onClick={downloadQRCode}
            >
              <Download className="h-4 w-4 mr-2" />
              Download QR
            </Button>
          </div>

          {/* Instructions */}
          <div className="text-center text-sm text-blue-700 bg-white p-3 rounded border">
            <p className="font-medium mb-1">How to pay:</p>
            <ol className="text-left space-y-1">
              <li>1. Open any UPI app (Google Pay, PhonePe, Paytm, etc.)</li>
              <li>2. Scan this QR code OR copy UPI ID</li>
              <li>3. Verify amount: ₹{amount}</li>
              <li>4. Complete payment with your UPI PIN</li>
              <li>5. Return here and click "Payment Completed"</li>
            </ol>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UPIQRCode;
