
import React from 'react';
import Layout from '../components/Layout';
import { Card, CardContent } from '@/components/ui/card';
import { UtensilsCrossed, Heart, Clock, Award } from 'lucide-react';

const AboutPage: React.FC = () => {
  return (
    <Layout title="About Us" showBackButton={true}>
      <div className="space-y-8">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-orange-100 to-yellow-100 rounded-2xl p-8 text-center food-pattern">
          <div className="bg-gradient-to-br from-orange-500 to-red-500 p-4 rounded-2xl mx-auto mb-6 w-20 h-20 flex items-center justify-center shadow-lg">
            <UtensilsCrossed size={40} className="text-white" />
          </div>
          <h1 className="text-3xl font-bold text-orange-600 mb-3">Food Hall Express</h1>
          <p className="text-gray-600 text-lg">Fresh Food, Fast Service, Great Taste</p>
        </div>
        
        {/* Our Story Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6 text-orange-600">Our Story</h2>
          <Card className="border-orange-200 shadow-lg">
            <CardContent className="p-8">
              <p className="text-gray-700 leading-relaxed text-lg mb-4">
                Food Hall Express was founded with a simple mission: to bring you delicious, freshly prepared meals with the convenience of modern technology. Our team of passionate chefs and food enthusiasts work tirelessly to create an exceptional dining experience.
              </p>
              <p className="text-gray-700 leading-relaxed text-lg">
                From classic comfort foods to international cuisines, we pride ourselves on using high-quality ingredients and innovative cooking techniques to deliver meals that satisfy both your taste buds and your busy lifestyle.
              </p>
            </CardContent>
          </Card>
        </div>
        
        {/* Team Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6 text-orange-600">Meet Our Team</h2>
          <Card className="border-orange-200 shadow-lg">
            <CardContent className="p-8">
              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="md:w-1/3">
                  <div className="rounded-2xl overflow-hidden aspect-square shadow-lg">
                    <img 
                      src="https://images.unsplash.com/photo-1577219491135-ce391730fb2c?q=80&w=1477&auto=format&fit=crop" 
                      alt="Restaurant Team" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                <div className="md:w-2/3">
                  <h3 className="text-xl font-semibold mb-3 text-orange-600">Our Culinary Team</h3>
                  <p className="italic text-gray-600 mb-4">"Great food brings people together, and technology makes it accessible to everyone."</p>
                  <p className="text-gray-700 text-lg">
                    Our experienced chefs bring together traditional cooking methods with modern presentation, ensuring every dish meets our high standards for flavor, quality, and visual appeal.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* What Makes Us Different */}
        <div>
          <h2 className="text-2xl font-bold mb-6 text-orange-600">What Makes Us Different</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <Card className="border-orange-200 shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-gradient-to-br from-orange-500 to-red-500 rounded-xl p-3">
                    <Clock size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-2">Quick Service</h3>
                    <p className="text-gray-600">Fast preparation and delivery without compromising on quality or taste.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-orange-200 shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-3">
                    <Heart size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-2">Fresh Ingredients</h3>
                    <p className="text-gray-600">We source fresh, high-quality ingredients daily to ensure the best flavors.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-orange-200 shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-3">
                    <Award size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-2">Quality Standards</h3>
                    <p className="text-gray-600">Every dish is prepared to meet our strict quality and safety standards.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-orange-200 shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-3">
                    <UtensilsCrossed size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-2">Diverse Menu</h3>
                    <p className="text-gray-600">A wide variety of cuisines and dishes to satisfy every palate and preference.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Gallery Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6 text-orange-600">Our Kitchen</h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="rounded-2xl overflow-hidden aspect-video shadow-lg">
              <img 
                src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?q=80&w=1470&auto=format&fit=crop" 
                alt="Modern Kitchen" 
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="rounded-2xl overflow-hidden aspect-video shadow-lg">
              <img 
                src="https://images.unsplash.com/photo-1504754524776-8f4f37790ca0?q=80&w=1470&auto=format&fit=crop" 
                alt="Food Preparation" 
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="rounded-2xl overflow-hidden aspect-video shadow-lg">
              <img 
                src="https://images.unsplash.com/photo-1559329007-40df8a9345d8?q=80&w=1470&auto=format&fit=crop" 
                alt="Restaurant Interior" 
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="rounded-2xl overflow-hidden aspect-video shadow-lg">
              <img 
                src="https://images.unsplash.com/photo-1567521464027-f127ff144326?q=80&w=1470&auto=format&fit=crop" 
                alt="Happy Customers" 
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AboutPage;
