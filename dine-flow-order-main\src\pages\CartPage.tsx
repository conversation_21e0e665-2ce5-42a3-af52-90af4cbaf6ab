
import React from 'react';
import { Link } from 'react-router-dom';
import { Trash, Plus, Minus } from 'lucide-react';
import Layout from '../components/Layout';
import { useCart } from '../context/CartContext';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent } from '@/components/ui/card';

const CartPage: React.FC = () => {
  const { 
    items, 
    updateQuantity, 
    removeFromCart, 
    getSubtotal,
    getTipAmount,
    getTotal,
    tipPercentage,
    setTipPercentage
  } = useCart();

  const tipOptions = [0, 5, 10, 15];

  // Format price to Indian Rupee format
  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;

  if (items.length === 0) {
    return (
      <Layout title="Your Cart" showBackButton={true}>
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="mb-6 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="8" cy="21" r="1"/>
              <circle cx="19" cy="21" r="1"/>
              <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/>
            </svg>
          </div>
          <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
          <p className="text-gray-500 mb-6">Add some delicious items from the menu</p>
          <Link to="/">
            <Button>Browse Menu</Button>
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Your Cart" showBackButton={true}>
      <div className="space-y-4 mb-6">
        {items.map((item) => (
          <Card key={item.id} className="overflow-hidden">
            <div className="flex">
              <div className="w-24 h-24">
                <img 
                  src={item.image} 
                  alt={item.name} 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardContent className="flex-1 p-3">
                <div className="flex justify-between">
                  <h3 className="font-medium">{item.name}</h3>
                  <button 
                    onClick={() => removeFromCart(item.id)}
                    className="text-gray-400 hover:text-destructive"
                    aria-label="Remove item"
                  >
                    <Trash size={16} />
                  </button>
                </div>
                
                <div className="text-sm text-primary font-medium mb-3">{formatPrice(item.price)}</div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 border border-gray-200 rounded-full overflow-hidden">
                    <button 
                      onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      className="bg-gray-100 text-gray-600 w-8 h-8 flex items-center justify-center hover:bg-gray-200"
                      aria-label="Decrease quantity"
                    >
                      <Minus size={16} />
                    </button>
                    <span className="w-5 text-center">{item.quantity}</span>
                    <button 
                      onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      className="bg-primary text-white w-8 h-8 flex items-center justify-center hover:bg-primary/90"
                      aria-label="Increase quantity"
                    >
                      <Plus size={16} />
                    </button>
                  </div>
                  
                  <span className="font-semibold">
                    {formatPrice(item.price * item.quantity)}
                  </span>
                </div>
              </CardContent>
            </div>
          </Card>
        ))}
      </div>

      {/* Tip Selection */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <h3 className="font-medium mb-3">Add a tip?</h3>
          <div className="flex flex-wrap gap-2">
            {tipOptions.map((tip) => (
              <button
                key={tip}
                className={`px-4 py-2 rounded-full text-sm font-medium ${
                  tipPercentage === tip
                    ? 'bg-primary text-white'
                    : 'bg-white border border-gray-200 text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setTipPercentage(tip)}
                aria-label={`${tip}% tip`}
              >
                {tip}%
              </button>
            ))}
            <div className="relative">
              <input
                type="number"
                min="0"
                max="100"
                value={tipPercentage !== 0 && tipPercentage !== 5 && tipPercentage !== 10 && tipPercentage !== 15 ? tipPercentage : ''}
                onChange={(e) => setTipPercentage(Number(e.target.value))}
                placeholder="Custom %"
                className="px-4 py-2 rounded-full text-sm border border-gray-200 w-24 focus:outline-none focus:ring-2 focus:ring-primary/50"
                aria-label="Custom tip percentage"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card className="mb-6 bg-white border-primary/20">
        <CardContent className="p-4">
          <h3 className="font-medium mb-3">Order Summary</h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-500">Subtotal</span>
              <span>{formatPrice(getSubtotal())}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-500">Tip ({tipPercentage}%)</span>
              <span>{formatPrice(getTipAmount())}</span>
            </div>
            
            <Separator className="my-2" />
            
            <div className="flex justify-between font-semibold text-base">
              <span>Total</span>
              <span className="text-primary">{formatPrice(getTotal())}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Link to="/checkout">
        <Button className="w-full py-6 text-lg bg-primary hover:bg-primary/90">
          Proceed to Checkout
        </Button>
      </Link>
    </Layout>
  );
};

export default CartPage;
