
import React from 'react';
import MenuItemCard from './MenuItemCard';
import { MenuItem } from '../context/CartContext';
import { motion } from 'framer-motion';
import { Spark<PERSON>, Star } from 'lucide-react';

interface MenuCategoryProps {
  title: string;
  items: MenuItem[];
}

const MenuCategory: React.FC<MenuCategoryProps> = ({ title, items }) => {
  if (items.length === 0) return null;
  
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    show: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };
  
  return (
    <div className="mb-12">
      <div className="flex items-center gap-4 mb-8 animate-slide-in-left">
        <div className="relative">
          <h2 className="text-2xl sm:text-3xl font-bold font-merriweather gradient-text animate-gradient">
            {title}
          </h2>
          <div className="absolute -top-1 -right-1">
            <Star size={16} className="text-yellow-400 animate-pulse-gentle floating-element" />
          </div>
        </div>
        
        <div className="h-1 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 flex-grow rounded-full animate-gradient"></div>
        
        <div className="flex items-center gap-1 bg-orange-100 px-3 py-1 rounded-full">
          <Sparkles size={14} className="text-orange-600 animate-pulse-gentle" />
          <span className="text-orange-600 font-medium text-sm">{items.length} dishes</span>
        </div>
      </div>
      
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        variants={containerVariants}
        initial="hidden"
        animate="show"
      >
        {items.map((item, index) => (
          <motion.div 
            key={item.id} 
            variants={itemVariants} 
            whileHover={{ 
              scale: 1.05, 
              rotate: Math.random() > 0.5 ? 1 : -1,
              transition: { duration: 0.3 }
            }}
            className={`animate-slide-up-fade stagger-${Math.min(index + 1, 5)}`}
          >
            <MenuItemCard item={item} />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default MenuCategory;
