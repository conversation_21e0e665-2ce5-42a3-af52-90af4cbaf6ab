
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useCart } from '@/context/CartContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isUserRegistered } = useCart();

  // During development - allow access to all pages
  // TODO: Re-enable authentication when ready for production
  const isDevelopment = true;
  
  if (isDevelopment || isUserRegistered) {
    return <>{children}</>;
  }

  return <Navigate to="/register" replace />;
};

export default ProtectedRoute;
