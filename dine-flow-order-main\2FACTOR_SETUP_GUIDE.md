# 🚀 2Factor.in OTP Integration Setup Guide

## ✅ Integration Complete!

Your Food Hall Express application has been successfully integrated with 2Factor.in OTP service, replacing Firebase SMS. Here's what you need to do to get it working:

## 📋 Step 1: Get Your 2Factor.in API Key

### 1.1 Create Account
1. Visit [2factor.in](https://2factor.in)
2. Click **Sign Up** 
3. Use your restaurant email (e.g., `<EMAIL>`)
4. Complete email verification
5. **No phone/GSTIN needed for free tier!**

### 1.2 Get API Key
1. Login to your 2Factor.in dashboard
2. Navigate to **API Keys** section
3. Copy your "Default API Key"
4. It will look like: `ccd903dd-3dee-11f0-a562-0200cd936042`

## 🔧 Step 2: Configure API Key

### 2.1 Update Configuration File
1. Open: `src/lib/twoFactor.ts`
2. Find line 6: `const TWO_FACTOR_API_KEY = "YOUR_2FACTOR_API_KEY_HERE";`
3. Replace `YOUR_2FACTOR_API_KEY_HERE` with your actual API key
4. Save the file

**Example:**
```typescript
const TWO_FACTOR_API_KEY = "ccd903dd-3dee-11f0-a562-0200cd936042";
```

## 🎯 Step 3: Test the Integration

### 3.1 Start Development Server
```bash
npm run dev
```

### 3.2 Test Registration Flow
1. Navigate to: `http://localhost:8080/register`
2. Fill in details:
   - **Name:** `Test User`
   - **Phone:** `**********` (your actual number)
   - **Email:** `<EMAIL>` (optional)
3. Click **"Send OTP"**
4. Check your phone for SMS with 6-digit OTP
5. Enter the OTP and click **"Verify & Continue"**

## 📱 Expected User Experience

### Customer Registration Flow:
1. **Scan QR Code** → Redirects to registration page
2. **Enter Details** → Name, phone number, email (optional)
3. **Click "Send OTP"** → Real SMS sent via 2Factor.in
4. **Receive SMS** → 6-digit OTP delivered within 2-6 seconds
5. **Enter OTP** → With 5-minute countdown timer
6. **Verification Success** → Redirected to menu page

### Features Included:
- ✅ **Real SMS delivery** via 2Factor.in API
- ✅ **5-minute OTP expiry** with countdown timer
- ✅ **Resend OTP** functionality (60-second cooldown)
- ✅ **Attempt limiting** (3 attempts per OTP)
- ✅ **Phone number validation** (10-digit Indian numbers)
- ✅ **Error handling** for network issues
- ✅ **User-friendly messages** for all scenarios

## 💰 Cost & Limits

### Free Tier Benefits:
- **50 OTP/month** completely free
- **No DLT registration** required
- **No GST/business verification** needed
- **Instant activation**

### Paid Tier (When needed):
- **₹0.12 per OTP** beyond free tier
- **Custom sender ID** available
- **DLT registration** required for >50 OTPs/month

## 🔍 Troubleshooting

### Issue: "API key not configured" error
**Solution:** Update the API key in `src/lib/twoFactor.ts`

### Issue: OTP not received
**Debug Steps:**
1. Check 2Factor.in dashboard for balance
2. Verify phone number format (10 digits, no +91)
3. Check spam/promotional SMS folder
4. Try with different phone number

### Issue: "Network error"
**Solution:** 
1. Check internet connection
2. Verify API key is correct
3. Check 2Factor.in service status

## 🔒 Security Features

### Built-in Security:
- **OTP expiry** (5 minutes)
- **Attempt limiting** (3 tries per OTP)
- **Rate limiting** (prevents spam)
- **Phone validation** (Indian format only)
- **Session management** (secure storage)

## 📊 Monitoring & Analytics

### Track Usage:
1. Login to 2Factor.in dashboard
2. View **SMS Reports** for delivery status
3. Monitor **Balance** and usage
4. Check **API Logs** for debugging

## 🚀 Production Deployment

### When Ready for Production:
1. **Upgrade to paid plan** if >50 users/month
2. **Register for DLT** (mandatory for commercial use)
3. **Apply for custom sender ID** (optional)
4. **Set up monitoring** and alerts

## 📞 Support

### Need Help?
- **2Factor.in Support:** <EMAIL>
- **Documentation:** https://2factor.in/API/
- **Dashboard:** https://2factor.in/login

---

## 🎉 You're All Set!

Your Food Hall Express application now uses 2Factor.in for reliable, cost-effective OTP delivery. The integration provides:

- **Better reliability** than Firebase
- **Lower costs** (free tier + affordable pricing)
- **No billing setup** required initially
- **Indian phone number optimization**
- **Professional SMS delivery**

**Next Steps:**
1. Update your API key
2. Test with real phone numbers
3. Launch your QR code ordering system!

Your customers will now receive professional OTP messages for secure phone verification! 📱✨
