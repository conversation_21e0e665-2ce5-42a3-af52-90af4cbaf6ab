import { useEffect, useCallback, useRef } from 'react';

// Performance monitoring hook
export const usePerformance = (componentName: string) => {
  const renderStartTime = useRef<number>(Date.now());
  const mountTime = useRef<number | null>(null);

  useEffect(() => {
    mountTime.current = Date.now();
    const renderTime = mountTime.current - renderStartTime.current;
    
    if (import.meta.env.DEV) {
      console.log(`🚀 ${componentName} rendered in ${renderTime}ms`);
    }

    // In production, send to analytics
    if (!import.meta.env.DEV && renderTime > 100) {
      // Report slow renders
      console.warn(`Slow render detected: ${componentName} took ${renderTime}ms`);
    }

    return () => {
      if (mountTime.current) {
        const totalTime = Date.now() - mountTime.current;
        if (import.meta.env.DEV) {
          console.log(`🔄 ${componentName} unmounted after ${totalTime}ms`);
        }
      }
    };
  }, [componentName]);

  const measureOperation = useCallback((operationName: string, operation: () => void) => {
    const start = performance.now();
    operation();
    const end = performance.now();
    const duration = end - start;

    if (import.meta.env.DEV) {
      console.log(`⏱️ ${operationName} took ${duration.toFixed(2)}ms`);
    }

    return duration;
  }, []);

  return { measureOperation };
};

// Image loading optimization hook
export const useImagePreload = (imageSrc: string) => {
  useEffect(() => {
    const img = new Image();
    img.src = imageSrc;
  }, [imageSrc]);
};

// Intersection Observer hook for lazy loading
export const useIntersectionObserver = (
  callback: (isIntersecting: boolean) => void,
  options?: IntersectionObserverInit
) => {
  const elementRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => callback(entry.isIntersecting),
      { threshold: 0.1, ...options }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [callback, options]);

  return elementRef;
};

// Memory usage monitoring
export const useMemoryMonitor = () => {
  useEffect(() => {
    if (!import.meta.env.DEV) return;

    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        console.log('Memory Usage:', {
          used: `${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`,
        });
      }
    };

    const interval = setInterval(checkMemory, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);
};

// Network status monitoring
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionType, setConnectionType] = useState<string>('unknown');

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check connection type if available
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      setConnectionType(connection.effectiveType || 'unknown');

      const handleConnectionChange = () => {
        setConnectionType(connection.effectiveType || 'unknown');
      };

      connection.addEventListener('change', handleConnectionChange);

      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
        connection.removeEventListener('change', handleConnectionChange);
      };
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return { isOnline, connectionType };
};

// Bundle size analyzer (development only)
export const analyzeBundleSize = () => {
  if (!import.meta.env.DEV) return;

  const scripts = Array.from(document.querySelectorAll('script[src]'));
  const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));

  console.group('📦 Bundle Analysis');
  
  scripts.forEach((script: HTMLScriptElement) => {
    if (script.src.includes('localhost') || script.src.includes('127.0.0.1')) {
      console.log(`Script: ${script.src.split('/').pop()}`);
    }
  });

  styles.forEach((style: HTMLLinkElement) => {
    if (style.href.includes('localhost') || style.href.includes('127.0.0.1')) {
      console.log(`Style: ${style.href.split('/').pop()}`);
    }
  });

  console.groupEnd();
};

// Core Web Vitals monitoring
export const useCoreWebVitals = () => {
  useEffect(() => {
    if (!import.meta.env.DEV) return;

    // Largest Contentful Paint
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        console.log('FID:', entry.processingStart - entry.startTime);
      });
    });

    fidObserver.observe({ entryTypes: ['first-input'] });

    return () => {
      observer.disconnect();
      fidObserver.disconnect();
    };
  }, []);
};
