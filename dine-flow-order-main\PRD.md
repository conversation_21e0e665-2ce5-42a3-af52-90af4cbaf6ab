
# Product Requirements Document (PRD)
# Food Hall Express - QR Code Ordering System

## 1. Project Overview

### 1.1 Product Vision
Food Hall Express is a modern, QR code-based food ordering system designed for restaurants and food halls. Customers can scan QR codes at their tables to access the menu, place orders, and make payments, while restaurant staff manage orders through a comprehensive admin dashboard.

### 1.2 Target Users
- **Primary Users**: Restaurant customers dining at tables
- **Secondary Users**: Restaurant staff, chefs, and administrators
- **Tertiary Users**: Restaurant managers and owners

### 1.3 Key Value Propositions
- Contactless ordering experience for customers
- Real-time order management for restaurant staff
- Reduced wait times and improved efficiency
- Mobile-first responsive design
- Firebase-based phone authentication for security

## 2. Core Features & Functionality

### 2.1 Customer-Facing Features

#### 2.1.1 User Registration & Authentication
- Phone number-based registration with OTP verification
- Firebase Authentication integration
- Guest user support with basic information collection
- Secure session management

#### 2.1.2 Menu Browsing
- Category-based menu organization (Starters, Main Course, Desserts, Beverages, etc.)
- Search functionality across all menu items
- Advanced filtering options:
  - Vegetarian dishes filter
  - Spicy food filter
  - Bestseller items filter
- High-quality food images with descriptions
- Real-time pricing display

#### 2.1.3 Shopping Cart & Ordering
- Add/remove items from cart
- Quantity management
- Order total calculation with tax
- Table number automatic detection via QR code
- Order review before checkout
- Order confirmation with estimated delivery time

#### 2.1.4 Payment Integration
- Secure payment processing
- Multiple payment method support
- Order receipt generation
- Payment confirmation notifications

### 2.2 Admin/Staff Features

#### 2.2.1 Order Management Dashboard
- Real-time order tracking with status updates:
  - **New**: Newly received orders requiring acknowledgment
  - **Preparing**: Orders assigned to chefs and being prepared
  - **Ready**: Completed orders ready for delivery
  - **Delivered**: Orders delivered to customers
- Order priority management with visual indicators
- Automatic sorting by order time (latest first)

#### 2.2.2 Chef Assignment System
- Customizable chef assignment for new orders
- Available chef roster:
  - Chef Rajesh
  - Chef Priya
  - Chef Kumar
  - Chef Anita
  - Chef Vikram
- Real-time chef workload tracking
- Order acknowledgment time monitoring

#### 2.2.3 Time Management & Alerts
- **Acknowledgment Alerts**:
  - Normal: 0-5 minutes (blue indicator)
  - Warning: 5-10 minutes (orange indicator)
  - Urgent: 10+ minutes (red indicator with urgent flag)
- Real-time countdown timers for order preparation
- Estimated completion time tracking
- Overdue order notifications

#### 2.2.4 Order Analytics
- Order count statistics by status
- Table-wise order tracking
- Time-based order analytics
- Revenue tracking per order

## 3. Technical Specifications

### 3.1 Technology Stack
- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with Shadcn UI components
- **Authentication**: Firebase Auth (Phone/SMS OTP)
- **State Management**: React Context API
- **Routing**: React Router DOM
- **Data Fetching**: TanStack React Query
- **Icons**: Lucide React
- **UI Components**: Shadcn UI library
- **Form Handling**: React Hook Form with Zod validation
- **Notifications**: Sonner toast notifications

### 3.2 Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Shadcn UI components
│   ├── Layout.tsx      # Main layout wrapper
│   ├── MenuCategory.tsx # Menu category display
│   ├── MenuItemCard.tsx # Individual menu item cards
│   └── SkeletonLoading.tsx # Loading skeletons
├── context/            # React Context providers
│   └── CartContext.tsx # Shopping cart state management
├── data/               # Static data and mock data
│   └── menuData.ts     # Menu items and categories
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries and configurations
│   ├── firebase.ts     # Firebase configuration
│   └── utils.ts        # Utility functions
├── pages/              # Page components
│   ├── UserRegistrationPage.tsx # User registration with OTP
│   ├── MenuPage.tsx    # Main menu browsing page
│   ├── CartPage.tsx    # Shopping cart page
│   ├── CheckoutPage.tsx # Order checkout and payment
│   ├── AdminPage.tsx   # Admin dashboard for order management
│   ├── SuccessPage.tsx # Order confirmation page
│   ├── AccountPage.tsx # User account management
│   ├── AboutPage.tsx   # About the restaurant
│   ├── ContactPage.tsx # Contact information
│   └── NotFound.tsx    # 404 error page
└── App.tsx             # Main application component with routing
```

## 4. User Flows

### 4.1 Customer Ordering Flow
1. Customer scans QR code at table
2. Lands on registration page (if new user)
3. Enters phone number and receives OTP
4. Verifies phone number and completes registration
5. Browses menu by categories or searches for items
6. Adds desired items to cart
7. Reviews cart and proceeds to checkout
8. Completes payment
9. Receives order confirmation with table number

### 4.2 Admin Order Management Flow
1. Admin/Staff accesses admin dashboard
2. Views new orders requiring acknowledgment
3. Assigns chef to new orders (customizable selection)
4. Monitors order preparation progress
5. Updates order status (Preparing → Ready → Delivered)
6. Tracks time for each order stage
7. Receives alerts for orders requiring attention

## 5. Data Models

### 5.1 User Model
```typescript
interface User {
  id: string;
  name: string;
  phone: string;
  email?: string;
  isVerified: boolean;
}
```

### 5.2 Menu Item Model
```typescript
interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  isBestSeller?: boolean;
  isVegetarian?: boolean;
  isSpicy?: boolean;
}
```

### 5.3 Order Model
```typescript
interface Order {
  id: string;
  tableNumber: number;
  items: OrderItem[];
  total: number;
  status: 'new' | 'preparing' | 'ready' | 'delivered';
  timestamp: Date;
  chef?: string;
  acknowledgedAt?: Date;
  estimatedTime?: number;
}
```

## 6. Success Metrics

### 6.1 Customer Experience Metrics
- Order completion rate
- Average time from menu access to order placement
- Customer satisfaction scores
- Return customer rate

### 6.2 Operational Metrics
- Average order acknowledgment time
- Kitchen preparation efficiency
- Order accuracy rate
- Table turnover time

## 7. Future Enhancements

### 7.1 Phase 2 Features
- Real-time order notifications for customers
- Loyalty program integration
- Multi-language support
- Dietary restriction filters
- Customer feedback system

### 7.2 Phase 3 Features
- Integration with POS systems
- Advanced analytics dashboard
- Inventory management
- Staff scheduling integration
- Customer order history

## 8. Security & Privacy

### 8.1 Data Protection
- Phone number encryption
- Secure Firebase authentication
- PCI DSS compliance for payments
- GDPR compliance for data handling

### 8.2 Access Control
- Role-based access for admin features
- Secure session management
- API endpoint protection
- Input validation and sanitization

## 9. Deployment & Infrastructure

### 9.1 Hosting Requirements
- Static site hosting (Lovable platform)
- Firebase backend services
- CDN for image delivery
- SSL certificate for security

### 9.2 Environment Configuration
- Firebase project configuration
- Payment gateway integration
- Environment-specific settings
- Monitoring and logging setup
