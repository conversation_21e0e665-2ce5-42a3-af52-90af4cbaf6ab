// 2Factor.in OTP Service Integration
// Replace Firebase SMS with 2Factor.in API

// 2Factor.in API Configuration
const TWO_FACTOR_API_KEY = "ccd903dd-3dee-11f0-a562-0200cd936042"; // Your 2Factor.in API key
const TWO_FACTOR_BASE_URL = "https://2factor.in/API/V1";
const TWO_FACTOR_SMS_URL = "https://2factor.in/API/R1"; // SMS-specific endpoint

// Phone number formatting utility
export const formatPhone = (phone: string): string => {
  // Remove any non-digit characters and country code prefixes
  const cleaned = phone.replace(/\D/g, '');
  
  // Remove +91, 91, or 0 prefix if present
  const formatted = cleaned.replace(/^(91|0)/, '');
  
  // Return 10-digit number
  return formatted.slice(-10);
};

// Format phone for 2Factor API (needs country code)
export const formatPhoneFor2Factor = (phone: string): string => {
  const cleaned = formatPhone(phone);
  return `91${cleaned}`; // Add 91 country code
};

// Generate 6-digit OTP
export const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send OTP via 2Factor.in API
export const send2FactorOTP = async (phone: string): Promise<{ success: boolean; sessionId?: string; error?: string }> => {
  try {
    // API key is already configured, no need to check

    const formattedPhone = formatPhoneFor2Factor(phone);
    const otp = generateOTP();
    
    // Store OTP in localStorage for verification (in production, use secure backend)
    const otpData = {
      code: otp,
      phone: formattedPhone,
      expiresAt: Date.now() + 300000, // 5 minutes
      attempts: 0
    };
    
    localStorage.setItem(`otp_${formattedPhone}`, JSON.stringify(otpData));
    
    // Use CORRECT SMS-specific endpoint to force SMS delivery (not voice)
    // Based on 2Factor.in documentation: /SMS/ endpoint sends TEXT MESSAGE
    // /VOICE/ endpoint sends PHONE CALL - we must avoid this!

    // Method 1: SMS with approved template (most reliable)
    const smsWithTemplate = `${TWO_FACTOR_BASE_URL}/${TWO_FACTOR_API_KEY}/SMS/${formattedPhone}/${otp}/FoodHallOTP`;

    // Method 2: SMS with AUTOGEN template (default SMS template)
    const smsWithAutogen = `${TWO_FACTOR_BASE_URL}/${TWO_FACTOR_API_KEY}/SMS/${formattedPhone}/${otp}/AUTOGEN`;

    // Method 3: SMS with AUTOGEN2 template (alternative default)
    const smsWithAutogen2 = `${TWO_FACTOR_BASE_URL}/${TWO_FACTOR_API_KEY}/SMS/${formattedPhone}/${otp}/AUTOGEN2`;

    // Method 4: Basic SMS without template (last resort)
    const basicSmsUrl = `${TWO_FACTOR_BASE_URL}/${TWO_FACTOR_API_KEY}/SMS/${formattedPhone}/${otp}`;

    // IMPORTANT: All URLs use /SMS/ NOT /VOICE/ to ensure TEXT MESSAGE delivery

    console.log('Sending SMS OTP to:', formattedPhone, 'with OTP:', otp);
    console.log('Using SMS-only endpoints (NOT voice calls)');

    // Try multiple SMS methods to ensure SMS delivery (not voice)
    const smsUrls = [
      smsWithTemplate,  // Custom template (if approved)
      smsWithAutogen,   // AUTOGEN template
      smsWithAutogen2,  // AUTOGEN2 template
      basicSmsUrl       // Basic SMS
    ];

    for (let i = 0; i < smsUrls.length; i++) {
      const currentUrl = smsUrls[i];
      console.log(`Trying SMS method ${i + 1}:`, currentUrl);

      try {
        const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(currentUrl)}`;

        const proxyResponse = await fetch(proxyUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        const proxyResult = await proxyResponse.json();

        // Parse the actual 2Factor response from proxy
        let result: any;
        try {
          result = JSON.parse(proxyResult.contents);
          console.log('2Factor SMS API Response via proxy:', result);

          // Handle V1 API response format
          console.log('Checking response status...');
          console.log('result.Status:', result.Status);
          console.log('result.Details:', result.Details);

          if (result.Status === 'Success') {
            console.log('✅ SMS API returned success with method', i + 1);
            return {
              success: true,
              sessionId: result.Details || formattedPhone
            };
          } else {
            console.log(`❌ SMS method ${i + 1} failed:`, result.Details);

            // If this is the last method, return the error
            if (i === smsUrls.length - 1) {
              let errorMessage = result.Details || 'Failed to send SMS OTP';

              // Check for common restriction errors
              if (errorMessage.includes('not authorized') || errorMessage.includes('restricted')) {
                errorMessage = 'Account restricted. Please verify your 2Factor.in account to send OTP to any number.';
              } else if (errorMessage.includes('test mode') || errorMessage.includes('demo')) {
                errorMessage = 'Account in test mode. Please activate your 2Factor.in account for full access.';
              } else if (errorMessage.includes('balance') || errorMessage.includes('credit')) {
                errorMessage = 'Insufficient balance. Please check your 2Factor.in account balance.';
              } else if (errorMessage.includes('Invalid API Key')) {
                errorMessage = 'Invalid API key. Please check your 2Factor.in API key configuration.';
              } else if (errorMessage.includes('template')) {
                errorMessage = 'SMS template not approved. Please check your 2Factor.in templates or contact support.';
              }

              return {
                success: false,
                error: errorMessage
              };
            }
            // Continue to next method
          }
        } catch (parseError) {
          console.error(`Failed to parse response for method ${i + 1}:`, proxyResult);
          if (i === smsUrls.length - 1) {
            return {
              success: false,
              error: 'Invalid response from SMS service'
            };
          }
          // Continue to next method
        }
      } catch (fetchError) {
        console.error(`Network error for method ${i + 1}:`, fetchError);
        if (i === smsUrls.length - 1) {
          return {
            success: false,
            error: 'Network error. Please try again.'
          };
        }
        // Continue to next method
      }
    }
  } catch (error) {
    console.error('2Factor OTP Error:', error);
    return {
      success: false,
      error: 'Network error. Please try again.'
    };
  }
};

// Verify OTP
export const verify2FactorOTP = async (phone: string, enteredOTP: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const formattedPhone = formatPhoneFor2Factor(phone);
    const storedData = localStorage.getItem(`otp_${formattedPhone}`);
    
    if (!storedData) {
      return {
        success: false,
        error: 'OTP expired or not found. Please request a new OTP.'
      };
    }
    
    const otpData = JSON.parse(storedData);
    
    // Check if OTP is expired
    if (Date.now() > otpData.expiresAt) {
      localStorage.removeItem(`otp_${formattedPhone}`);
      return {
        success: false,
        error: 'OTP has expired. Please request a new OTP.'
      };
    }
    
    // Check attempt limit
    if (otpData.attempts >= 3) {
      localStorage.removeItem(`otp_${formattedPhone}`);
      return {
        success: false,
        error: 'Too many failed attempts. Please request a new OTP.'
      };
    }
    
    // Verify OTP
    if (otpData.code === enteredOTP) {
      localStorage.removeItem(`otp_${formattedPhone}`);
      return {
        success: true
      };
    } else {
      // Increment attempts
      otpData.attempts += 1;
      localStorage.setItem(`otp_${formattedPhone}`, JSON.stringify(otpData));
      
      return {
        success: false,
        error: `Invalid OTP. ${3 - otpData.attempts} attempts remaining.`
      };
    }
  } catch (error) {
    console.error('OTP Verification Error:', error);
    return {
      success: false,
      error: 'Verification failed. Please try again.'
    };
  }
};

// Check if OTP is still valid
export const isOTPValid = (phone: string): boolean => {
  const formattedPhone = formatPhoneFor2Factor(phone);
  const storedData = localStorage.getItem(`otp_${formattedPhone}`);
  
  if (!storedData) return false;
  
  const otpData = JSON.parse(storedData);
  return Date.now() < otpData.expiresAt;
};

// Get remaining time for OTP
export const getOTPRemainingTime = (phone: string): number => {
  const formattedPhone = formatPhoneFor2Factor(phone);
  const storedData = localStorage.getItem(`otp_${formattedPhone}`);

  if (!storedData) return 0;

  const otpData = JSON.parse(storedData);
  const remaining = Math.max(0, otpData.expiresAt - Date.now());
  return Math.ceil(remaining / 1000); // Return seconds
};

// Test 2Factor.in account by sending a test SMS
export const test2FactorAccount = async (): Promise<{ success: boolean; message: string; balance?: string }> => {
  try {
    // Test by sending OTP to your registered number (**********)
    const testPhone = "************"; // Your registered number
    const testOTP = "123456";

    // Use SMS endpoint (NOT VOICE) to ensure text message delivery
    const apiUrl = `${TWO_FACTOR_BASE_URL}/${TWO_FACTOR_API_KEY}/SMS/${testPhone}/${testOTP}/AUTOGEN`;
    const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(apiUrl)}`;

    console.log('Testing 2Factor account with test SMS...');

    const response = await fetch(proxyUrl);
    const proxyResult = await response.json();
    const result = JSON.parse(proxyResult.contents);

    console.log('Test SMS result:', result);

    if (result.Status === 'Success') {
      return {
        success: true,
        message: 'Account is working! Test SMS sent successfully. You can send OTP to any number.',
        balance: 'API working'
      };
    } else {
      return {
        success: false,
        message: `Account issue: ${result.Details || 'API key may be invalid or account has restrictions'}`
      };
    }
  } catch (error) {
    return {
      success: false,
      message: 'Unable to test account. Please check your internet connection and API key.'
    };
  }
};
