# Implementing 2Factor.in OTP Verification for Food Hall Express  

## System Compatibility Validation  
Based on the PRD's technical stack (React 18 + Firebase + TypeScript) and 2Factor.in's free tier capabilities:  

✅ **No DLT/GST Required** for ≤50 OTP/month demo usage  
✅ Compatible with existing Firebase phone auth architecture  
✅ Supports Indian mobile numbers (+91 format)  
✅ Pre-approved transactional templates available  

---

## Step 1: 2Factor.in Account Setup  

### 1.1 Registration Process  
1. Visit [2factor.in](https://2factor.in)  
2. Click **Sign Up** → Use restaurant email (e.g., `<EMAIL>`)  
3. Complete email verification → No phone/GSTIN needed  
4. Navigate to **API Keys** → Copy "Default API Key"  

```typescript  
// Store in Firebase config  
const TWO_FACTOR_API_KEY = "ccd903dd-3dee-11f0-a562-0200cd936042";  
```

---

## Step 2: Frontend Integration  

### 2.1 Modify Registration Component  
Update `UserRegistrationPage.tsx`:  
```tsx  
const handleOTPRequest = async () => {
  const phone = formatPhone(phoneInput.value); // +91XXXXXXXXXX → 91XXXXXXXXXX
  try {
    const response = await fetch(
      'https://us-central1-foodhallexpress.cloudfunctions.net/send2FactorOTP',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phone })
      }
    );
    
    if (!response.ok) throw new Error('OTP send failed');
    setOTPSent(true);
  } catch (error) {
    toast.error('Failed to send OTP');
  }
};
```

---

## Step 3: Backend Implementation  

### 3.1 Cloud Function for OTP Generation  
```typescript  
// send2FactorOTP.ts  
export const send2FactorOTP = functions.https.onRequest(async (req, res) => {
  const phone = req.body.phone.replace(/^\+?91?/, ''); // Format: XXXXXXXXXX
  
  const otp = Math.floor(1000 + Math.random() * 9000); // 4-digit OTP
  
  // Store OTP in Firestore with 5min TTL  
  await admin.firestore().collection('otps').doc(phone).set({
    code: otp.toString(),
    expiresAt: Date.now() + 300000
  });

  // 2Factor API Request  
  const apiUrl = `https://2factor.in/API/V1/${TWO_FACTOR_API_KEY}/SMS/${phone}/${otp}`;
  
  try {
    const apiResponse = await fetch(apiUrl);
    const result = await apiResponse.json();
    
    if (result.Status === 'Success') {
      res.status(200).json({ success: true });
    } else {
      res.status(400).json({ error: 'OTP delivery failed' });
    }
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});
```

---

## Step 4: OTP Verification Flow  

### 4.1 Verification Endpoint  
```typescript  
// verify2FactorOTP.ts  
export const verify2FactorOTP = functions.https.onCall(async (data) => {
  const { phone, code } = data;
  const otpDoc = await admin.firestore().collection('otps').doc(phone).get();

  if (!otpDoc.exists) throw new functions.https.HttpsError('not-found', 'OTP expired');
  
  const { code: storedCode, expiresAt } = otpDoc.data()!;
  
  if (Date.now() > expiresAt) {
    await otpDoc.ref.delete();
    throw new functions.https.HttpsError('deadline-exceeded', 'OTP expired');
  }
  
  if (storedCode !== code) {
    throw new functions.https.HttpsError('invalid-argument', 'Invalid OTP');
  }

  await otpDoc.ref.delete();
  return { success: true, userId: generateFirebaseUID(phone) };
});
```

---

## Technical Considerations  

### 1. Phone Number Formatting Utility  
```typescript  
// utils/phoneFormatter.ts  
export const formatPhone = (phone: string): string => {
  return phone.replace(/^(\+91|0|91)/, '').slice(0, 10);
};
```

### 2. Error Handling Strategy  
```tsx  
// Enhanced error states  

  {otpError ? 'Retry Verification' : 'Verify OTP'}

```

---

## Compliance & Limitations  

### Free Tier Constraints  
| Aspect               | Detail                          |
|----------------------|---------------------------------|
| Monthly OTP Limit    | 50                              |  
| Sender ID            | Default: `2FACTR`               |
| Template Customization | Fixed message format         |
| DLT Compliance       | Not required for ≤50 OTPs/month |

---

## Testing Protocol  

### 1. Local Emulation  
```bash  
# Run Firebase emulator suite  
firebase emulators:start --only functions,firestore  

# Test OTP flow  
curl -X POST http://localhost:5001/foodhallexpress/us-central1/send2FactorOTP \
  -H "Content-Type: application/json" \
  -d '{"phone":"9876543210"}'
```

### 2. Live Testing Checklist  
1. Verify SMS delivery within 2-6 seconds  
2. Confirm OTP validation works across retries  
3. Monitor 2Factor dashboard for usage metrics  

---

## Scaling to Production  

### Transition Checklist  
1. **DLT Registration**: Required for >50 OTPs/month  
2. **Custom Sender ID**: Apply via 2Factor dashboard  
3. **Pricing Plan**: ₹0.12/OTP beyond free tier  

```mermaid  
graph TD  
A[OTP Request] --> B{Free Tier Available?}  
B -->|Yes| C[Send via 2Factor]  
B -->|No| D[Switch to Paid Plan]  
```

---

## Security Best Practices  

### 1. OTP Storage Guidelines  
```typescript  
// Firestore security rules  
match /otps/{phone} {  
  allow read, write: if request.auth != null;  
  allow delete: true;  
}  
```

### 2. Rate Limiting  
```typescript  
// Firebase Functions rate limiter  
const rateLimit = require('express-rate-limit');  

const limiter = rateLimit({  
  windowMs: 15 * 60 * 1000, // 15 minutes  
  max: 3 // Limit each IP to 3 OTP requests per window  
});  
```

---

## Troubleshooting Guide  

### Common Issues & Solutions  
**Issue**: "Invalid API Key" Error  
- **Solution**:  
  ```bash  
  firebase functions:config:set twofactor.key="NEW_API_KEY"  
  firebase deploy --only functions  
  ```

**Issue**: OTP Not Delivered  
- **Debug Steps**:  
  1. Check 2Factor balance in dashboard  
  2. Verify phone number formatting  
  3. Test API endpoint with Postman  

---

## Conclusion  
This implementation provides a compliant demo system using 2Factor.in's free tier without complex registrations. The React/Firebase integration maintains the PRD's authentication flow while adding SMS OTP capabilities. For production scaling, transition to paid plans with DLT registration when exceeding 50 users/month.

