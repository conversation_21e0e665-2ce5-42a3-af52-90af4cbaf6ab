
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { useNavigate } from 'react-router-dom';
import { useCart } from '@/context/CartContext';
import { toast } from 'sonner';
import { User, Phone, Mail, Shield, Sparkles, ChefHat } from 'lucide-react';
import { send2FactorOTP, verify2FactorOTP, formatPhone, getOTPRemainingTime, test2FactorAccount } from '@/lib/twoFactor';

// 2Factor.in OTP integration - no global declarations needed

const UserRegistrationPage: React.FC = () => {
  const [step, setStep] = useState<'details' | 'otp'>('details');
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [otpTimer, setOtpTimer] = useState<number>(0);
  const navigate = useNavigate();
  const { setUserInfo } = useCart();

  // Timer for OTP expiry countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (otpTimer > 0) {
      interval = setInterval(() => {
        setOtpTimer((prev) => {
          if (prev <= 1) {
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [otpTimer]);

  // Format timer display
  const formatTimer = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Test 2Factor.in account status
  const testAccountStatus = async () => {
    setIsLoading(true);
    try {
      const result = await test2FactorAccount();
      if (result.success) {
        toast.success(`Account Status: ${result.message}. Balance: ${result.balance || 'N/A'}`);
      } else {
        toast.error(`Account Issue: ${result.message}`);
      }
    } catch (error) {
      toast.error('Failed to check account status');
    }
    setIsLoading(false);
  };

  const handleDetailsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim() || !phone.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (phone.length !== 10) {
      toast.error('Please enter a valid 10-digit phone number');
      return;
    }

    // Validate phone number format (should be all digits)
    if (!/^\d{10}$/.test(phone)) {
      toast.error('Phone number should contain only digits');
      return;
    }

    setIsLoading(true);

    try {
      // Format phone number for 2Factor.in
      const formattedPhone = formatPhone(phone);

      // Store guest info temporarily
      sessionStorage.setItem('guestName', name.trim());
      sessionStorage.setItem('guestEmail', email.trim());
      sessionStorage.setItem('guestPhone', `+91${formattedPhone}`);

      // Send OTP via 2Factor.in
      console.log('Attempting to send OTP to:', formattedPhone);
      const result = await send2FactorOTP(formattedPhone);
      console.log('OTP send result:', result);

      if (result.success) {
        setSessionId(result.sessionId || '');
        setOtpTimer(300); // 5 minutes countdown
        setIsLoading(false);
        setStep('otp');
        toast.success(`OTP sent to +91${formattedPhone}. Check your phone for SMS.`);
      } else {
        setIsLoading(false);
        console.error('OTP send failed:', result.error);
        toast.error(result.error || 'Failed to send OTP. Please try again.');
      }
    } catch (error: any) {
      setIsLoading(false);
      console.error('Error sending OTP:', error);
      toast.error('Failed to send OTP. Please check your phone number and try again.');
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (otp.length !== 6) {
      toast.error('Please enter the complete 6-digit OTP');
      return;
    }

    if (otpTimer <= 0) {
      toast.error('OTP has expired. Please request a new OTP.');
      return;
    }

    setIsLoading(true);

    try {
      // Get stored phone number
      const storedPhone = sessionStorage.getItem('guestPhone') || '';
      const phoneNumber = storedPhone.replace('+91', '');

      // Verify OTP using 2Factor.in
      const result = await verify2FactorOTP(phoneNumber, otp);

      if (result.success) {
        // Get stored info
        const storedName = sessionStorage.getItem('guestName') || '';
        const storedEmail = sessionStorage.getItem('guestEmail') || '';

        // Store user info in context
        setUserInfo({
          name: storedName,
          phone: storedPhone,
          email: storedEmail || undefined
        });

        // Mark as verified
        sessionStorage.setItem('isVerified', 'true');

        setIsLoading(false);
        toast.success('Phone verified! Welcome to Food Hall Express');
        navigate('/');
      } else {
        setIsLoading(false);
        toast.error(result.error || 'Invalid OTP. Please try again.');
      }
    } catch (error: any) {
      setIsLoading(false);
      console.error('Error verifying OTP:', error);
      toast.error('Verification failed. Please try again.');
    }
  };

  const resendOtp = async () => {
    const storedPhone = sessionStorage.getItem('guestPhone');
    if (!storedPhone) {
      toast.error('Phone number not found');
      return;
    }

    setIsLoading(true);

    try {
      // Format phone number for 2Factor.in
      const phoneNumber = storedPhone.replace('+91', '');

      // Resend OTP via 2Factor.in
      const result = await send2FactorOTP(phoneNumber);

      if (result.success) {
        setSessionId(result.sessionId || '');
        setOtpTimer(300); // Reset 5 minutes countdown
        setOtp(''); // Clear previous OTP input
        setIsLoading(false);
        toast.success(`OTP resent to ${storedPhone}`);
      } else {
        setIsLoading(false);
        toast.error(result.error || 'Failed to resend OTP. Please try again.');
      }
    } catch (error: any) {
      setIsLoading(false);
      console.error('Error resending OTP:', error);
      toast.error('Failed to resend OTP. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50 pattern-dots flex items-center justify-center p-4">
      {/* Floating decorative elements */}
      <div className="fixed top-10 left-10 text-orange-300 animate-float">
        <ChefHat size={48} />
      </div>
      <div className="fixed top-20 right-20 text-pink-300 animate-float-delayed">
        <Sparkles size={32} />
      </div>
      <div className="fixed bottom-20 left-20 text-purple-300 animate-float">
        <User size={40} />
      </div>

      <Card className="w-full max-w-md shadow-2xl border-0 bg-white/95 backdrop-blur-sm glow-orange animate-scale-in">
        <CardHeader className="text-center pb-6">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center mb-4 animate-pulse-gentle">
            {step === 'details' ? (
              <User className="text-white" size={32} />
            ) : (
              <Shield className="text-white" size={32} />
            )}
          </div>
          <CardTitle className="text-2xl font-bold gradient-text">
            {step === 'details' ? 'Welcome to Food Hall' : 'Verify Your Phone'}
          </CardTitle>
          <p className="text-gray-600 mt-2">
            {step === 'details' 
              ? 'Please provide your details to continue' 
              : `Enter the OTP sent to ${sessionStorage.getItem('guestPhone') || phone}`
            }
          </p>
        </CardHeader>

        <CardContent>
          {step === 'details' ? (
            <form onSubmit={handleDetailsSubmit} className="space-y-6">
              <div className="space-y-2 animate-slide-up-fade stagger-1">
                <Label htmlFor="name" className="text-sm font-medium flex items-center gap-2">
                  <User size={16} className="text-orange-500" />
                  Full Name *
                </Label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Enter your full name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="border-orange-200 focus:border-orange-500 transition-colors"
                  required
                />
              </div>

              <div className="space-y-2 animate-slide-up-fade stagger-2">
                <Label htmlFor="phone" className="text-sm font-medium flex items-center gap-2">
                  <Phone size={16} className="text-orange-500" />
                  Phone Number *
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="Enter your phone number (without +91)"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value.replace(/\D/g, ''))}
                  className="border-orange-200 focus:border-orange-500 transition-colors"
                  maxLength={10}
                  required
                />
              </div>

              <div className="space-y-2 animate-slide-up-fade stagger-3">
                <Label htmlFor="email" className="text-sm font-medium flex items-center gap-2">
                  <Mail size={16} className="text-orange-500" />
                  Email Address (Optional)
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email (optional)"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="border-orange-200 focus:border-orange-500 transition-colors"
                />
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full btn-primary animate-slide-up-fade stagger-4"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Sending OTP...
                  </div>
                ) : (
                  'Send OTP'
                )}
              </Button>

              {/* 2Factor.in OTP - No reCAPTCHA needed */}
            </form>
          ) : (
            <form onSubmit={handleOtpSubmit} className="space-y-6">
              <div className="space-y-4 animate-slide-up-fade">
                <div className="text-center">
                  <Label className="text-sm font-medium block">
                    Enter 6-digit OTP
                  </Label>
                  {otpTimer > 0 && (
                    <p className="text-xs text-gray-500 mt-1">
                      OTP expires in: <span className="font-mono text-orange-600">{formatTimer(otpTimer)}</span>
                    </p>
                  )}
                  {otpTimer <= 0 && (
                    <p className="text-xs text-red-500 mt-1">
                      OTP has expired. Please request a new one.
                    </p>
                  )}
                </div>
                <div className="flex justify-center">
                  <InputOTP
                    maxLength={6}
                    value={otp}
                    onChange={setOtp}
                  >
                    <InputOTPGroup>
                      <InputOTPSlot index={0} className="border-orange-200" />
                      <InputOTPSlot index={1} className="border-orange-200" />
                      <InputOTPSlot index={2} className="border-orange-200" />
                      <InputOTPSlot index={3} className="border-orange-200" />
                      <InputOTPSlot index={4} className="border-orange-200" />
                      <InputOTPSlot index={5} className="border-orange-200" />
                    </InputOTPGroup>
                  </InputOTP>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resendOtp}
                  disabled={isLoading || otpTimer > 240} // Disable for first 60 seconds
                  className="flex-1 border-orange-200 text-orange-600 hover:bg-orange-50 disabled:opacity-50"
                >
                  {isLoading ? 'Sending...' : otpTimer > 240 ? `Wait ${formatTimer(otpTimer - 240)}` : 'Resend OTP'}
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || otp.length !== 6}
                  className="flex-1 btn-primary"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Verifying...
                    </div>
                  ) : (
                    'Verify & Continue'
                  )}
                </Button>
              </div>

              <Button
                type="button"
                variant="ghost"
                onClick={() => setStep('details')}
                className="w-full text-gray-500 hover:text-gray-700"
              >
                ← Back to Details
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UserRegistrationPage;
