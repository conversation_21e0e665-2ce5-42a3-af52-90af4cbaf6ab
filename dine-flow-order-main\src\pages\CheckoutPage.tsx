
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { useCart } from '../context/CartContext';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent } from '@/components/ui/card';

const UPI_OPTIONS = [
  { id: 'googlepay', name: 'Google Pay', icon: 'https://cdn-icons-png.flaticon.com/512/6124/6124998.png' },
  { id: 'phonepe', name: 'PhonePe', icon: 'https://cdn-icons-png.flaticon.com/512/6124/6124997.png' },
  { id: 'paytm', name: 'Paytm', icon: 'https://cdn-icons-png.flaticon.com/512/6124/6124999.png' },
  { id: 'bhim', name: '<PERSON><PERSON><PERSON> UPI', icon: 'https://cdn-icons-png.flaticon.com/512/10268/10268684.png' },
];

const CheckoutPage: React.FC = () => {
  const { 
    items, 
    tableNumber, 
    getSubtotal, 
    getTipAmount, 
    getTotal,
    tipPercentage,
    clearCart
  } = useCart();
  
  const [paymentMethod, setPaymentMethod] = useState<string>('cash');
  const [upiOption, setUpiOption] = useState<string>('googlepay');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // Format price to Indian Rupee format
  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;

  const handleSubmitOrder = () => {
    setLoading(true);
    
    if (paymentMethod === 'upi') {
      // Simulate redirect to UPI payment
      toast({
        title: "Redirecting to payment",
        description: `Taking you to ${UPI_OPTIONS.find(opt => opt.id === upiOption)?.name}...`,
        duration: 3000,
      });
      
      setTimeout(() => {
        clearCart();
        navigate('/success', { 
          state: { 
            paymentMethod: 'upi',
            upiOption: upiOption
          } 
        });
        setLoading(false);
      }, 2000);
    } else {
      // Cash payment
      setTimeout(() => {
        clearCart();
        navigate('/success', { 
          state: { 
            paymentMethod: 'cash'
          } 
        });
        setLoading(false);
      }, 1500);
    }
  };

  return (
    <Layout title="Checkout" showBackButton={true} showCart={false}>
      <Card className="mb-6 border-primary/20">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                <path d="M3 11a8 8 0 0 1 16 0"></path>
                <path d="M3 11v3a8 8 0 0 0 16 0v-3"></path>
                <path d="M12 22v-9"></path>
                <path d="M8 14v1"></path>
                <path d="M16 14v1"></path>
              </svg>
            </div>
            <div>
              <h3 className="text-sm text-gray-500">Your Table</h3>
              <p className="text-xl font-semibold">Table #{tableNumber}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardContent className="p-4">
          <h3 className="font-medium mb-3">Order Items</h3>
          
          <div className="space-y-3">
            {items.map((item) => (
              <div key={item.id} className="flex justify-between text-sm">
                <span>{item.quantity} × {item.name}</span>
                <span>{formatPrice(item.price * item.quantity)}</span>
              </div>
            ))}
          </div>
          
          <Separator className="my-3" />
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Subtotal</span>
              <span>{formatPrice(getSubtotal())}</span>
            </div>
            
            {tipPercentage > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Tip ({tipPercentage}%)</span>
                <span>{formatPrice(getTipAmount())}</span>
              </div>
            )}
            
            <div className="flex justify-between font-semibold mt-2">
              <span>Total</span>
              <span className="text-primary">{formatPrice(getTotal())}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardContent className="p-4">
          <h3 className="font-medium mb-4">Payment Method</h3>
          
          <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
            <div className="flex items-center space-x-2 mb-4 pb-4 border-b">
              <RadioGroupItem value="upi" id="upi" />
              <Label htmlFor="upi" className="flex-1 cursor-pointer">
                <div className="flex items-center justify-between">
                  <span className="font-medium">UPI Payment</span>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="bg-blue-100 text-blue-600 px-2 py-1 rounded">Pay online</span>
                  </div>
                </div>
              </Label>
            </div>
            
            {paymentMethod === 'upi' && (
              <div className="mb-4 pl-6">
                <div className="grid grid-cols-2 gap-3">
                  {UPI_OPTIONS.map(option => (
                    <button
                      key={option.id}
                      className={`flex items-center gap-2 p-3 rounded-lg border ${
                        upiOption === option.id 
                          ? 'border-primary bg-primary/5' 
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                      onClick={() => setUpiOption(option.id)}
                    >
                      <img src={option.icon} alt={option.name} className="w-6 h-6" />
                      <span className="text-sm font-medium">{option.name}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="cash" id="cash" />
              <Label htmlFor="cash" className="flex-1 cursor-pointer">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Cash on Delivery</span>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded">Pay at table</span>
                  </div>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      <Button 
        className="w-full py-6 text-lg bg-primary hover:bg-primary/90" 
        disabled={loading}
        onClick={handleSubmitOrder}
      >
        {loading ? "Processing..." : "Place Order"}
      </Button>
    </Layout>
  );
};

export default CheckoutPage;
