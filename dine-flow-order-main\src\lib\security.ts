// Security utilities and validation functions
import { z } from 'zod';

// Input validation schemas
export const phoneSchema = z.string()
  .regex(/^[6-9]\d{9}$/, 'Please enter a valid 10-digit Indian mobile number')
  .transform(phone => phone.replace(/\D/g, ''));

export const nameSchema = z.string()
  .min(2, 'Name must be at least 2 characters')
  .max(50, 'Name must be less than 50 characters')
  .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces');

export const emailSchema = z.string()
  .email('Please enter a valid email address')
  .optional();

export const otpSchema = z.string()
  .length(6, 'OTP must be exactly 6 digits')
  .regex(/^\d{6}$/, 'OTP can only contain numbers');

// User registration validation
export const userRegistrationSchema = z.object({
  name: nameSchema,
  phone: phoneSchema,
  email: emailSchema,
});

// Input sanitization
export const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, ''); // Remove event handlers
};

// Rate limiting for OTP requests
class RateLimiter {
  private attempts: Map<string, { count: number; lastAttempt: number }> = new Map();
  private readonly maxAttempts = 3;
  private readonly windowMs = 15 * 60 * 1000; // 15 minutes

  canAttempt(identifier: string): boolean {
    const now = Date.now();
    const record = this.attempts.get(identifier);

    if (!record) {
      this.attempts.set(identifier, { count: 1, lastAttempt: now });
      return true;
    }

    // Reset if window has passed
    if (now - record.lastAttempt > this.windowMs) {
      this.attempts.set(identifier, { count: 1, lastAttempt: now });
      return true;
    }

    // Check if under limit
    if (record.count < this.maxAttempts) {
      record.count++;
      record.lastAttempt = now;
      return true;
    }

    return false;
  }

  getRemainingTime(identifier: string): number {
    const record = this.attempts.get(identifier);
    if (!record || record.count < this.maxAttempts) return 0;

    const elapsed = Date.now() - record.lastAttempt;
    return Math.max(0, this.windowMs - elapsed);
  }
}

export const otpRateLimiter = new RateLimiter();

// Secure session management
export class SecureSession {
  private static readonly SESSION_KEY = 'food-hall-session';
  private static readonly EXPIRY_KEY = 'food-hall-session-expiry';

  static setSession(data: any, expiryHours: number = 24): void {
    const expiry = Date.now() + (expiryHours * 60 * 60 * 1000);
    
    try {
      localStorage.setItem(this.SESSION_KEY, JSON.stringify(data));
      localStorage.setItem(this.EXPIRY_KEY, expiry.toString());
    } catch (error) {
      console.error('Failed to set session:', error);
    }
  }

  static getSession<T>(): T | null {
    try {
      const expiry = localStorage.getItem(this.EXPIRY_KEY);
      if (!expiry || Date.now() > parseInt(expiry)) {
        this.clearSession();
        return null;
      }

      const data = localStorage.getItem(this.SESSION_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get session:', error);
      this.clearSession();
      return null;
    }
  }

  static clearSession(): void {
    localStorage.removeItem(this.SESSION_KEY);
    localStorage.removeItem(this.EXPIRY_KEY);
  }

  static isSessionValid(): boolean {
    const expiry = localStorage.getItem(this.EXPIRY_KEY);
    return expiry ? Date.now() < parseInt(expiry) : false;
  }
}

// Environment validation
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Check required environment variables
  const requiredVars = [
    'VITE_FIREBASE_API_KEY',
    'VITE_FIREBASE_AUTH_DOMAIN',
    'VITE_FIREBASE_PROJECT_ID',
    'VITE_TWO_FACTOR_API_KEY'
  ];

  requiredVars.forEach(varName => {
    if (!import.meta.env[varName]) {
      errors.push(`Missing required environment variable: ${varName}`);
    }
  });

  // Check for default/placeholder values
  if (import.meta.env.VITE_TWO_FACTOR_API_KEY === 'your_api_key_here') {
    errors.push('2Factor.in API key is still using placeholder value');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Security headers for API requests
export const getSecureHeaders = (): HeadersInit => {
  return {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };
};

// Validate API responses
export const validateApiResponse = (response: any): boolean => {
  if (!response || typeof response !== 'object') return false;
  
  // Check for common injection patterns
  const jsonString = JSON.stringify(response);
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+=/i,
    /<iframe/i,
    /eval\(/i
  ];

  return !dangerousPatterns.some(pattern => pattern.test(jsonString));
};

// Log security events
export const logSecurityEvent = (event: string, details: any = {}): void => {
  if (import.meta.env.DEV) {
    console.warn(`🔒 Security Event: ${event}`, details);
  }
  
  // In production, send to monitoring service
  // Example: sendToMonitoring({ event, details, timestamp: Date.now() });
};
