
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useCart } from '@/context/CartContext';
import Layout from '@/components/Layout';
import { User, Phone, Mail, LogOut, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

const AccountPage: React.FC = () => {
  const { tableNumber, userInfo, setUserInfo } = useCart();
  const navigate = useNavigate();

  const handleLogout = () => {
    setUserInfo(null);
    // Clear all session data
    sessionStorage.removeItem('guestName');
    sessionStorage.removeItem('guestEmail');
    sessionStorage.removeItem('guestPhone');
    sessionStorage.removeItem('isVerified');
    toast.success('Logged out successfully');
    navigate('/register');
  };

  const isVerified = sessionStorage.getItem('isVerified') === 'true';

  return (
    <Layout title="Account" showBackButton={true}>
      <div className="space-y-6">
        {/* User Profile Card */}
        <Card className="animate-slide-up-fade">
          <CardContent className="p-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center">
                <User className="text-white" size={24} />
              </div>
              <div>
                <h2 className="text-xl font-bold">{userInfo?.name || 'Guest User'}</h2>
                <p className="text-gray-500">Table #{tableNumber}</p>
                {isVerified && (
                  <div className="flex items-center gap-1 mt-1">
                    <Shield size={14} className="text-green-600" />
                    <span className="text-xs text-green-600 font-medium">Verified</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-4">
              {userInfo?.phone && (
                <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                  <Phone size={18} className="text-orange-600" />
                  <div>
                    <p className="text-sm text-gray-500">Phone Number</p>
                    <p className="font-medium">{userInfo.phone}</p>
                  </div>
                  {isVerified && (
                    <Shield size={16} className="text-green-600 ml-auto" />
                  )}
                </div>
              )}
              
              {userInfo?.email && (
                <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                  <Mail size={18} className="text-purple-600" />
                  <div>
                    <p className="text-sm text-gray-500">Email Address</p>
                    <p className="font-medium">{userInfo.email}</p>
                  </div>
                </div>
              )}
              
              <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">#</span>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Session ID</p>
                  <p className="font-medium text-gray-600">#{Math.floor(10000 + Math.random() * 90000)}</p>
                </div>
              </div>
            </div>

            <Button
              onClick={handleLogout}
              variant="outline"
              className="w-full mt-6 border-red-200 text-red-600 hover:bg-red-50"
            >
              <LogOut size={16} className="mr-2" />
              Logout
            </Button>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default AccountPage;
