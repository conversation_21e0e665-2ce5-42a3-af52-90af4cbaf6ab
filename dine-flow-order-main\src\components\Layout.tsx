
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ShoppingCart, Menu, ArrowLeft, Home, Info, Phone, UtensilsCrossed } from 'lucide-react';
import { useCart } from '../context/CartContext';
import UserAvatar from './UserAvatar';
import { Button } from './ui/button';
import { cn } from '@/lib/utils';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  showBackButton?: boolean;
  showCart?: boolean;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  title = "Food Hall Express",
  showBackButton = false,
  showCart = true,
}) => {
  const location = useLocation();
  const { getTotalItems } = useCart();
  const cartItemCount = getTotalItems();
  const isCartPage = location.pathname === '/cart';
  const isCheckoutPage = location.pathname === '/checkout';
  const isSuccessPage = location.pathname === '/success';

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-white/95 backdrop-blur-sm shadow-lg border-b border-orange-100">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center">
            {showBackButton && (
              <Link to="/" className="mr-3 p-2 hover:bg-orange-50 rounded-lg transition-colors">
                <ArrowLeft size={24} className="text-orange-600" />
              </Link>
            )}
            
            <Link to="/" className="flex items-center group">
              <div className="bg-gradient-to-br from-orange-500 to-red-500 p-2 rounded-xl mr-3 group-hover:scale-105 transition-transform shadow-lg">
                <UtensilsCrossed size={24} className="text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-orange-600 font-poppins">Food Hall</h1>
                <p className="text-xs text-gray-500 -mt-1">Express</p>
              </div>
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-gray-700 hover:text-orange-600 transition-colors font-semibold hover:scale-105 transform">Menu</Link>
            <Link to="/about" className="text-gray-700 hover:text-orange-600 transition-colors font-semibold hover:scale-105 transform">About</Link>
            <Link to="/contact" className="text-gray-700 hover:text-orange-600 transition-colors font-semibold hover:scale-105 transform">Contact</Link>
          </div>
          
          <div className="flex items-center space-x-3">
            {showCart && !isCartPage && !isCheckoutPage && !isSuccessPage && (
              <Link to="/cart" className="relative p-2 hover:bg-orange-50 rounded-lg transition-colors group">
                <ShoppingCart size={24} className="text-orange-600 group-hover:scale-110 transition-transform" />
                {cartItemCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center shadow-lg animate-pulse">
                    {cartItemCount}
                  </span>
                )}
              </Link>
            )}
            <UserAvatar />
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 container mx-auto px-4 py-8">
        {children}
      </main>

      {/* Navigation Footer */}
      <div className="block md:hidden sticky bottom-0 bg-white/95 backdrop-blur-sm border-t border-orange-100 shadow-lg">
        <div className="container mx-auto flex justify-around py-3">
          <Link to="/" className="flex flex-col items-center p-2 text-gray-600 hover:text-orange-600 transition-colors">
            <Home size={20} />
            <span className="text-xs mt-1 font-medium">Menu</span>
          </Link>
          <Link to="/about" className="flex flex-col items-center p-2 text-gray-600 hover:text-orange-600 transition-colors">
            <Info size={20} />
            <span className="text-xs mt-1 font-medium">About</span>
          </Link>
          <Link to="/cart" className="flex flex-col items-center p-2 text-gray-600 hover:text-orange-600 transition-colors relative">
            <ShoppingCart size={20} />
            {cartItemCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                {cartItemCount}
              </span>
            )}
            <span className="text-xs mt-1 font-medium">Cart</span>
          </Link>
          <Link to="/contact" className="flex flex-col items-center p-2 text-gray-600 hover:text-orange-600 transition-colors">
            <Phone size={20} />
            <span className="text-xs mt-1 font-medium">Contact</span>
          </Link>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-orange-50 to-yellow-50 border-t border-orange-100 py-6">
        <div className="container mx-auto px-4 text-center">
          <div className="flex justify-center mb-3">
            <div className="bg-gradient-to-br from-orange-500 to-red-500 p-2 rounded-xl">
              <UtensilsCrossed size={20} className="text-white" />
            </div>
          </div>
          <p className="text-gray-600 font-medium">© 2025 Food Hall Express • Delicious Meals, Quick Service</p>
          
          {/* Desktop Navigation Links */}
          <div className="hidden md:flex justify-center space-x-8 mt-3">
            <Link to="/" className="text-gray-500 hover:text-orange-600 transition-colors font-medium">Menu</Link>
            <Link to="/about" className="text-gray-500 hover:text-orange-600 transition-colors font-medium">About</Link>
            <Link to="/contact" className="text-gray-500 hover:text-orange-600 transition-colors font-medium">Contact</Link>
            <Link to="/account" className="text-gray-500 hover:text-orange-600 transition-colors font-medium">Account</Link>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
