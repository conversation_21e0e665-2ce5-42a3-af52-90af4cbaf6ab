
import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Clock, MapPin, ChefHat } from 'lucide-react';
import { useCart } from '../context/CartContext';

const SuccessPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { tableNumber } = useCart();
  const paymentMethod = location.state?.paymentMethod || 'cash';
  const upiOption = location.state?.upiOption;

  // Generate order ID
  const orderId = `ORD-${new Date().getFullYear()}-${Date.now()}`;

  useEffect(() => {
    // In a real app, this would send the order to your backend/admin system
    console.log('Order placed successfully:', {
      orderId,
      tableNumber,
      paymentMethod,
      timestamp: new Date().toISOString()
    });
  }, [orderId, tableNumber, paymentMethod]);

  const getPaymentMethodDisplay = () => {
    if (paymentMethod === 'upi') {
      const upiName = {
        'googlepay': 'Google Pay',
        'phonepe': 'PhonePe', 
        'paytm': 'Paytm',
        'bhim': 'BHIM UPI'
      }[upiOption] || 'UPI';
      return `Paid via ${upiName}`;
    }
    return 'Cash on Delivery';
  };

  return (
    <Layout title="Order Confirmed" showBackButton={false} showCart={false}>
      <div className="max-w-md mx-auto text-center space-y-6">
        {/* Success Icon */}
        <div className="flex justify-center mb-6">
          <div className="bg-green-100 p-6 rounded-full">
            <CheckCircle className="text-green-600" size={48} />
          </div>
        </div>

        {/* Success Message */}
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-green-600">Order Confirmed!</h1>
          <p className="text-gray-600">Thank you for your order. We're preparing your delicious meal.</p>
        </div>

        {/* Order Details Card */}
        <Card className="border-green-200">
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center justify-between py-2 border-b">
              <span className="text-gray-600">Order ID</span>
              <span className="font-mono font-semibold">{orderId}</span>
            </div>
            
            <div className="flex items-center justify-between py-2 border-b">
              <span className="text-gray-600 flex items-center gap-2">
                <MapPin size={16} />
                Table Number
              </span>
              <span className="font-semibold">#{tableNumber}</span>
            </div>
            
            <div className="flex items-center justify-between py-2 border-b">
              <span className="text-gray-600">Payment</span>
              <span className="font-semibold">{getPaymentMethodDisplay()}</span>
            </div>
            
            <div className="flex items-center justify-between py-2">
              <span className="text-gray-600 flex items-center gap-2">
                <Clock size={16} />
                Order Time
              </span>
              <span className="font-semibold">
                {new Date().toLocaleTimeString('en-US', { 
                  hour: '2-digit', 
                  minute: '2-digit',
                  hour12: true 
                })}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Status Update */}
        <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <ChefHat className="text-orange-600" size={24} />
              <div className="text-left">
                <p className="font-semibold text-orange-800">Your order is now with our kitchen!</p>
                <p className="text-sm text-orange-600">Estimated preparation time: 15-25 minutes</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-3 pt-4">
          <Button 
            className="w-full bg-gradient-to-r from-primary to-red-500 hover:from-red-500 hover:to-primary" 
            onClick={() => navigate('/')}
          >
            Order More Items
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={() => navigate('/admin')}
          >
            View Admin Dashboard
          </Button>
        </div>

        {/* Additional Info */}
        <div className="text-sm text-gray-500 bg-gray-50 p-4 rounded-lg">
          <p className="mb-2">📱 <strong>Track your order:</strong></p>
          <p>Your order has been sent to our kitchen team and will be prepared fresh for you.</p>
          <p className="mt-2">🔔 You'll be notified when your order is ready for pickup/delivery.</p>
        </div>
      </div>
    </Layout>
  );
};

export default SuccessPage;
