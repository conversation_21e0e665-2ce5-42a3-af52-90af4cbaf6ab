
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useCart } from '../context/CartContext';
import { Button } from './ui/button';
import { Plus } from 'lucide-react';

interface FavoriteDishProps {
  dish: {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    description: string; // Added missing description property
  }
}

const FavoriteDish: React.FC<FavoriteDishProps> = ({ dish }) => {
  const { addToCart } = useCart();
  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-24 overflow-hidden">
        <img 
          src={dish.image} 
          alt={dish.name} 
          className="w-full h-full object-cover"
        />
      </div>
      <CardContent className="p-3">
        <div className="flex justify-between items-center">
          <div>
            <h4 className="font-medium text-sm">{dish.name}</h4>
            <p className="text-primary text-xs">{formatPrice(dish.price)}</p>
          </div>
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-8 w-8 p-0 rounded-full"
            onClick={() => addToCart(dish)}
          >
            <Plus size={16} />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default FavoriteDish;
