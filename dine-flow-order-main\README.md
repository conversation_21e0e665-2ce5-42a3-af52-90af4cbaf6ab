
# Food Hall Express - QR Code Ordering System

A modern, contactless food ordering system built with React, TypeScript, and Firebase. Customers can scan QR codes at tables to browse menus, place orders, and make payments, while restaurant staff manage orders through a comprehensive admin dashboard.

## 🛠 Technology Stack & Dependencies

### Core Technologies
- **Frontend Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite (Development Server & Build Tool)
- **Package Manager**: npm/yarn/bun
- **Language**: TypeScript (.ts/.tsx files)
- **Styling**: Tailwind CSS with custom configuration

### Authentication & Backend
- **Firebase**: Version 11.8.1
  - Firebase Auth (Phone/SMS OTP verification)
  - Firebase Configuration for web apps
- **Authentication Flow**: Phone number + OTP verification

### UI Framework & Components
- **Shadcn UI**: Complete component library
- **Radix UI**: Underlying primitive components for Shadcn
  - @radix-ui/react-accordion: ^1.2.0
  - @radix-ui/react-alert-dialog: ^1.1.1
  - @radix-ui/react-avatar: ^1.1.0
  - @radix-ui/react-checkbox: ^1.1.1
  - @radix-ui/react-dialog: ^1.1.2
  - @radix-ui/react-dropdown-menu: ^2.1.1
  - @radix-ui/react-label: ^2.1.0
  - @radix-ui/react-popover: ^1.1.1
  - @radix-ui/react-progress: ^1.1.0
  - @radix-ui/react-radio-group: ^1.2.0
  - @radix-ui/react-scroll-area: ^1.1.0
  - @radix-ui/react-select: ^2.1.1
  - @radix-ui/react-separator: ^1.1.0
  - @radix-ui/react-slider: ^1.2.0
  - @radix-ui/react-slot: ^1.1.0
  - @radix-ui/react-switch: ^1.1.0
  - @radix-ui/react-tabs: ^1.1.0
  - @radix-ui/react-toast: ^1.2.1
  - @radix-ui/react-tooltip: ^1.1.4

### State Management & Data Fetching
- **React Context API**: Global state management (Cart, User info)
- **TanStack React Query**: Version 5.56.2 for server state management
- **React Hook Form**: Version 7.53.0 for form handling
- **Zod**: Version 3.23.8 for form validation and type safety

### Routing & Navigation
- **React Router DOM**: Version 6.26.2 for client-side routing

### Utility Libraries
- **Lucide React**: Version 0.462.0 (Icon library)
- **Class Variance Authority (CVA)**: Version 0.7.1 (CSS class utilities)
- **clsx**: Version 2.1.1 (Conditional CSS classes)
- **Tailwind Merge**: Version 2.5.2 (Merge Tailwind classes)
- **Date-fns**: Version 3.6.0 (Date manipulation)

### UI Enhancement Libraries
- **Framer Motion**: Version 12.12.1 (Animations)
- **Sonner**: Version 1.5.0 (Toast notifications)
- **Input OTP**: Version 1.2.4 (OTP input component)
- **React Day Picker**: Version 8.10.1 (Date picker)
- **Embla Carousel React**: Version 8.3.0 (Carousel component)
- **React Resizable Panels**: Version 2.1.3 (Resizable layouts)
- **Recharts**: Version 2.12.7 (Charts and graphs)
- **Vaul**: Version 0.9.3 (Drawer component)
- **CMDK**: Version 1.0.0 (Command palette)
- **Next Themes**: Version 0.3.0 (Theme management)
- **Tailwindcss Animate**: Version 1.0.7 (Animation utilities)

### Form Validation
- **@hookform/resolvers**: Version 3.9.0 (React Hook Form resolvers)
- **Zod**: Integrated with React Hook Form for type-safe validation

## 📦 Complete Dependencies List

```json
{
  "dependencies": {
    "@hookform/resolvers": "^3.9.0",
    "@radix-ui/react-accordion": "^1.2.0",
    "@radix-ui/react-alert-dialog": "^1.1.1",
    "@radix-ui/react-aspect-ratio": "^1.1.0",
    "@radix-ui/react-avatar": "^1.1.0",
    "@radix-ui/react-checkbox": "^1.1.1",
    "@radix-ui/react-collapsible": "^1.1.0",
    "@radix-ui/react-context-menu": "^2.2.1",
    "@radix-ui/react-dialog": "^1.1.2",
    "@radix-ui/react-dropdown-menu": "^2.1.1",
    "@radix-ui/react-hover-card": "^1.1.1",
    "@radix-ui/react-label": "^2.1.0",
    "@radix-ui/react-menubar": "^1.1.1",
    "@radix-ui/react-navigation-menu": "^1.2.0",
    "@radix-ui/react-popover": "^1.1.1",
    "@radix-ui/react-progress": "^1.1.0",
    "@radix-ui/react-radio-group": "^1.2.0",
    "@radix-ui/react-scroll-area": "^1.1.0",
    "@radix-ui/react-select": "^2.1.1",
    "@radix-ui/react-separator": "^1.1.0",
    "@radix-ui/react-slider": "^1.2.0",
    "@radix-ui/react-slot": "^1.1.0",
    "@radix-ui/react-switch": "^1.1.0",
    "@radix-ui/react-tabs": "^1.1.0",
    "@radix-ui/react-toast": "^1.2.1",
    "@radix-ui/react-toggle": "^1.1.0",
    "@radix-ui/react-toggle-group": "^1.1.0",
    "@radix-ui/react-tooltip": "^1.1.4",
    "@tanstack/react-query": "^5.56.2",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cmdk": "^1.0.0",
    "date-fns": "^3.6.0",
    "embla-carousel-react": "^8.3.0",
    "firebase": "^11.8.1",
    "framer-motion": "^12.12.1",
    "input-otp": "^1.2.4",
    "lucide-react": "^0.462.0",
    "next-themes": "^0.3.0",
    "react": "^18.3.1",
    "react-day-picker": "^8.10.1",
    "react-dom": "^18.3.1",
    "react-hook-form": "^7.53.0",
    "react-resizable-panels": "^2.1.3",
    "react-router-dom": "^6.26.2",
    "recharts": "^2.12.7",
    "sonner": "^1.5.0",
    "tailwind-merge": "^2.5.2",
    "tailwindcss-animate": "^1.0.7",
    "vaul": "^0.9.3",
    "zod": "^3.23.8"
  }
}
```

## 🚀 Quick Setup Instructions

### Prerequisites
- **Node.js**: Version 18 or higher
- **npm**, **yarn**, or **bun**: Package manager
- **Firebase Account**: For authentication setup

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd food-hall-express
   ```

2. **Install all dependencies**
   ```bash
   # Using npm
   npm install

   # Using yarn
   yarn install

   # Using bun
   bun install
   ```

3. **Firebase Configuration**
   - Create a new Firebase project at https://console.firebase.google.com
   - Enable Authentication → Phone provider
   - Copy your Firebase config and update `src/lib/firebase.ts`

4. **Start development server**
   ```bash
   # Using npm
   npm run dev

   # Using yarn
   yarn dev

   # Using bun
   bun dev
   ```

5. **Access the application**
   - Customer interface: `http://localhost:5173`
   - Registration page: `http://localhost:5173/register`
   - Admin dashboard: `http://localhost:5173/admin`

## 📁 Project Directory Structure

```
food-hall-express/
├── public/                     # Static assets
│   ├── favicon.ico
│   ├── placeholder.svg
│   └── robots.txt
├── src/                        # Source code
│   ├── components/             # Reusable UI components
│   │   ├── ui/                # Shadcn UI components (35+ components)
│   │   ├── Layout.tsx         # Main layout wrapper
│   │   ├── MenuCategory.tsx   # Menu category display
│   │   ├── MenuItemCard.tsx   # Menu item cards
│   │   ├── ProtectedRoute.tsx # Route protection
│   │   ├── SkeletonLoading.tsx # Loading states
│   │   ├── UserAvatar.tsx     # User avatar component
│   │   ├── OrderHistoryItem.tsx # Order history display
│   │   └── FavoriteDish.tsx   # Favorite dish component
│   ├── context/               # React Context providers
│   │   └── CartContext.tsx    # Shopping cart state
│   ├── data/                  # Static data
│   │   └── menuData.ts        # Menu items and categories
│   ├── hooks/                 # Custom React hooks
│   │   ├── use-mobile.tsx     # Mobile detection hook
│   │   └── use-toast.ts       # Toast notifications hook
│   ├── lib/                   # Utility libraries
│   │   ├── firebase.ts        # Firebase configuration
│   │   └── utils.ts           # Utility functions
│   ├── pages/                 # Page components (Main application pages)
│   │   ├── UserRegistrationPage.tsx # Phone + OTP registration
│   │   ├── MenuPage.tsx       # Main menu browsing
│   │   ├── CartPage.tsx       # Shopping cart management
│   │   ├── CheckoutPage.tsx   # Order checkout & payment
│   │   ├── AdminPage.tsx      # Order management dashboard
│   │   ├── SuccessPage.tsx    # Order confirmation
│   │   ├── AccountPage.tsx    # User profile management
│   │   ├── AboutPage.tsx      # Restaurant information
│   │   ├── ContactPage.tsx    # Contact details
│   │   ├── NotFound.tsx       # 404 error page
│   │   └── Index.tsx          # Fallback index page
│   ├── App.tsx                # Main app with routing
│   ├── main.tsx               # React app entry point
│   ├── index.css              # Global styles
│   ├── App.css                # App-specific styles
│   └── vite-env.d.ts          # Vite environment types
├── PRD.md                     # Product Requirements Document
├── README.md                  # This file
├── package.json               # Dependencies and scripts
├── package-lock.json          # Dependency lock file
├── vite.config.ts             # Vite configuration
├── tailwind.config.ts         # Tailwind CSS configuration
├── tsconfig.json              # TypeScript configuration
├── tsconfig.app.json          # App-specific TypeScript config
├── tsconfig.node.json         # Node-specific TypeScript config
├── postcss.config.js          # PostCSS configuration
├── components.json            # Shadcn UI configuration
├── eslint.config.js           # ESLint configuration
└── .gitignore                 # Git ignore rules
```

## 🔧 Development Tools & Configuration

### Build Tools
- **Vite**: Fast build tool and dev server
- **TypeScript**: Type safety and better development experience
- **ESLint**: Code linting and quality checks
- **PostCSS**: CSS processing

### Styling Tools
- **Tailwind CSS**: Utility-first CSS framework
- **Tailwind Animate**: Animation utilities
- **CSS Variables**: For theme customization

### Type Definitions
- All components have TypeScript definitions
- Strict type checking enabled
- Custom interfaces for menu items, orders, and user data

## 🌟 Key Features Implementation

### Customer Features
- **QR Code Access**: Table-based ordering system
- **Phone Authentication**: Firebase OTP verification
- **Menu Browsing**: Category filtering, search, dietary filters
- **Cart Management**: Add/remove items, quantity control
- **Checkout Process**: Order review and payment

### Admin Features
- **Real-time Dashboard**: Live order tracking
- **Chef Assignment**: Customizable staff assignment
- **Order Status Management**: New → Preparing → Ready → Delivered
- **Time Tracking**: Acknowledgment alerts and preparation timers
- **Priority Indicators**: Visual alerts for urgent orders

### Technical Features
- **Responsive Design**: Mobile-first approach
- **State Management**: React Context for global state
- **Form Validation**: React Hook Form + Zod schemas
- **Real-time Updates**: Live order status changes
- **Error Handling**: Comprehensive error boundaries
- **Loading States**: Skeleton components for better UX

## 🔒 Environment Setup

### Required Environment Variables
Create a `.env` file in the root directory:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

### Firebase Console Setup
1. Go to Firebase Console (https://console.firebase.google.com)
2. Create new project
3. Enable Authentication → Phone provider
4. Add web app and copy configuration
5. Update `src/lib/firebase.ts` with your config

## 🚀 Deployment

### Build for Production
```bash
npm run build
# or
yarn build
# or
bun build
```

### Deploy to Static Hosting
The built files will be in the `dist/` directory and can be deployed to:
- Vercel
- Netlify
- Firebase Hosting
- Any static hosting provider

## 📱 Browser Compatibility

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **Responsive Design**: Works on all screen sizes (320px+)

## 🔍 Troubleshooting

### Common Issues
1. **Firebase Auth Issues**: Check Firebase console configuration
2. **Build Errors**: Ensure all dependencies are installed
3. **Type Errors**: Check TypeScript configuration
4. **Styling Issues**: Verify Tailwind CSS setup

### Debug Mode
- Open browser developer tools
- Check console for error messages
- Verify network requests to Firebase
- Check local storage for user data

## 📞 Support & Documentation

- **Firebase Docs**: https://firebase.google.com/docs
- **React Docs**: https://react.dev
- **Tailwind CSS**: https://tailwindcss.com
- **Shadcn UI**: https://ui.shadcn.com
- **Vite Docs**: https://vitejs.dev

---

**Food Hall Express** - A complete QR code ordering solution for modern restaurants
