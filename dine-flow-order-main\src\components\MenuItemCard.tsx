
import React from 'react';
import { Plus, Minus, Star, Sparkles } from 'lucide-react';
import { useCart, MenuItem } from '../context/CartContext';
import { toast } from '@/components/ui/use-toast';
import { formatCurrency } from '@/lib/utils';

interface MenuItemCardProps {
  item: MenuItem;
}

const MenuItemCard: React.FC<MenuItemCardProps> = ({ item }) => {
  const { addToCart, items, updateQuantity } = useCart();
  const cartItem = items.find((i) => i.id === item.id);
  const quantity = cartItem?.quantity || 0;

  const handleAddToCart = () => {
    addToCart(item);
    
    // Show enhanced toast notification
    toast({
      title: "🎉 Added to cart",
      description: `${item.name} has been added to your cart.`,
      duration: 2000,
    });
    
    // Enhanced cart icon animation
    const cartIcon = document.querySelector('.cart-icon');
    if (cartIcon) {
      cartIcon.classList.add('animate-wiggle');
      setTimeout(() => {
        cartIcon.classList.remove('animate-wiggle');
      }, 500);
    }
  };

  const handleIncrement = () => {
    updateQuantity(item.id, quantity + 1);
  };

  const handleDecrement = () => {
    updateQuantity(item.id, quantity - 1);
  };

  const formattedPrice = formatCurrency(item.price);

  return (
    <div className="menu-card group hover-lift animate-scale-in">
      <div className="relative h-48 overflow-hidden">
        <img 
          src={item.image} 
          alt={item.name} 
          className="menu-image"
        />
        
        {/* Enhanced overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        {item.isBestSeller && (
          <div className="best-seller-tag flex items-center gap-1">
            <Star size={12} className="fill-current" />
            Best Seller
          </div>
        )}
        
        {/* Floating sparkle effects */}
        <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
          <Sparkles size={16} className="text-yellow-300 animate-pulse-gentle floating-element" />
        </div>
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
          <Sparkles size={12} className="text-pink-300 animate-pulse-gentle floating-delayed" />
        </div>
      </div>
      
      <div className="p-4 relative z-10">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-medium text-gray-800 group-hover:text-orange-600 transition-colors duration-300">{item.name}</h3>
          <span className="font-bold text-lg gradient-text animate-gradient">{formattedPrice}</span>
        </div>
        
        <p className="text-sm text-gray-500 mt-1 line-clamp-2 h-10 group-hover:text-gray-600 transition-colors duration-300">{item.description}</p>
        
        <div className="mt-4">
          {quantity > 0 ? (
            <div className="flex items-center justify-between animate-slide-in-left">
              <span className="text-sm text-gray-500 font-medium bg-orange-50 px-2 py-1 rounded-full">
                Qty: <span className="text-orange-600 font-bold">{quantity}</span>
              </span>
              <div className="flex items-center space-x-3">
                <button 
                  onClick={handleDecrement}
                  className="quantity-btn hover:bg-red-50 hover:text-red-600 interactive-icon"
                  aria-label="Decrease quantity"
                >
                  <Minus size={14} />
                </button>
                <span className="font-bold text-lg text-orange-600 min-w-[20px] text-center">
                  {quantity}
                </span>
                <button 
                  onClick={handleIncrement}
                  className="quantity-btn hover:bg-green-50 hover:text-green-600 interactive-icon"
                  aria-label="Increase quantity"
                >
                  <Plus size={14} />
                </button>
              </div>
            </div>
          ) : (
            <button 
              onClick={handleAddToCart} 
              className="w-full py-3 bg-gradient-to-r from-orange-500 to-red-500 hover:from-red-500 hover:to-orange-500 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2 hover:scale-105 hover:shadow-lg group/btn"
              aria-label="Add to cart"
            >
              <Plus size={18} className="group-hover/btn:rotate-90 transition-transform duration-300" />
              <span>Add to Cart</span>
              <Sparkles size={16} className="opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MenuItemCard;
