
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { formatDate } from '@/lib/utils';
import { useCart } from '@/context/CartContext';
import { MenuItem } from '@/context/CartContext';

export interface OrderHistoryEntry {
  id: string;
  date: Date;
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  paymentMethod: string;
}

const OrderHistoryItem: React.FC<{ order: OrderHistoryEntry }> = ({ order }) => {
  const { addToCart } = useCart();
  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;
  
  const reorder = () => {
    // Convert order items back to menu items and add them to cart
    order.items.forEach(item => {
      const menuItem: MenuItem = {
        id: item.id,
        name: item.name,
        price: item.price,
        description: "", // We don't have this in order history
        image: "", // We don't have this in order history
        category: "", // We don't have this in order history
      };
      
      for (let i = 0; i < item.quantity; i++) {
        addToCart(menuItem);
      }
    });
  };

  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div>
            <p className="text-sm text-gray-500">{formatDate(order.date)}</p>
            <p className="font-medium">{order.items.length} items · {formatPrice(order.total)}</p>
          </div>
          <div className="text-right">
            <span className="inline-block px-2 py-1 rounded-full bg-primary/10 text-primary text-xs">
              {order.paymentMethod}
            </span>
          </div>
        </div>
        
        <div className="space-y-1 mb-3">
          {order.items.map((item, index) => (
            <p key={index} className="text-sm">
              {item.quantity}x {item.name}
            </p>
          ))}
        </div>
        
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full" 
          onClick={reorder}
        >
          Reorder
        </Button>
      </CardContent>
    </Card>
  );
};

export default OrderHistoryItem;
