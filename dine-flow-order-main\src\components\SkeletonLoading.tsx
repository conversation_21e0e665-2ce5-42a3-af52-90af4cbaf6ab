
import React from 'react';
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from 'framer-motion';

export const MenuItemSkeleton = () => {
  return (
    <div className="menu-card">
      <Skeleton className="w-full h-48" />
      <div className="p-4">
        <div className="flex items-start justify-between">
          <Skeleton className="h-6 w-2/3 mb-2" />
          <Skeleton className="h-6 w-16" />
        </div>
        <Skeleton className="h-4 w-full mb-1" />
        <Skeleton className="h-4 w-3/4 mb-4" />
        <Skeleton className="h-10 w-full rounded-md" />
      </div>
    </div>
  );
};

export const CategorySkeleton = () => {
  // Animation variants for loading skeletons
  const container = {
    show: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const item = {
    hidden: { opacity: 0.3 },
    show: { 
      opacity: 1,
      transition: {
        repeat: Infinity,
        repeatType: "reverse" as const,
        duration: 1
      }
    }
  };

  return (
    <div className="mb-10">
      <div className="flex items-center gap-3 mb-6">
        <Skeleton className="h-6 w-1/4" />
        <div className="h-0.5 bg-gray-200 flex-grow rounded-full"></div>
      </div>
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
        variants={container}
        initial="hidden"
        animate="show"
      >
        {[...Array(6)].map((_, index) => (
          <motion.div key={index} variants={item}>
            <MenuItemSkeleton />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};
