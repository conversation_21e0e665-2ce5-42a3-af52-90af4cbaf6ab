
import React from 'react';
import { Link } from 'react-router-dom';
import { User } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const UserAvatar: React.FC = () => {
  return (
    <Link to="/account" className="relative p-2 hover:bg-orange-50 rounded-lg transition-all duration-200 hover:scale-105" aria-label="View Account">
      <Avatar className="h-8 w-8 bg-gradient-to-br from-orange-100 to-yellow-100 border-2 border-orange-200 shadow-md">
        <AvatarFallback className="bg-gradient-to-br from-orange-100 to-yellow-100">
          <User size={16} className="text-orange-600" />
        </AvatarFallback>
      </Avatar>
    </Link>
  );
};

export default UserAvatar;
