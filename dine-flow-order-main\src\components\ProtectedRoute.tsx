
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useCart } from '@/context/CartContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isUserRegistered } = useCart();

  // Environment-based development mode
  const isDevelopment = import.meta.env.VITE_ENABLE_DEVELOPMENT_MODE === 'true' || import.meta.env.DEV;
  
  if (isDevelopment || isUserRegistered) {
    return <>{children}</>;
  }

  return <Navigate to="/register" replace />;
};

export default ProtectedRoute;
