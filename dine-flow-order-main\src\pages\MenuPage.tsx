
import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout';
import MenuCategory from '../components/MenuCategory';
import { CategorySkeleton } from '../components/SkeletonLoading';
import { menuCategories, getMenuItemsByCategory, menuItems } from '../data/menuData';
import { useCart } from '../context/CartContext';
import { Input } from '@/components/ui/input';
import { Search, Star, Sparkles, Clock, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Toggle } from '@/components/ui/toggle';

const MenuPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState<string>(menuCategories[0].id);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredItems, setFilteredItems] = useState(getMenuItemsByCategory(activeCategory));
  const [vegOnly, setVegOnly] = useState(false);
  const [spicyOnly, setSpicyOnly] = useState(false);
  const [bestSellersOnly, setBestSellersOnly] = useState(false);
  const { setTableNumber } = useCart();

  // Simulate QR scan loading
  useEffect(() => {
    // Simulate loading time and table number detection from QR
    const timer = setTimeout(() => {
      setLoading(false);
      // Set table number (in a real app, this would come from QR code)
      setTableNumber(12);
    }, 1500);

    return () => clearTimeout(timer);
  }, [setTableNumber]);

  // Handle search and filters
  useEffect(() => {
    let items = searchQuery 
      ? menuItems.filter(item => 
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
          item.description.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : getMenuItemsByCategory(activeCategory);

    // Apply veg filter if enabled
    if (vegOnly) {
      // This is a mock filter - in a real app you would have a vegetarian flag on menu items
      const vegDishIds = ['starter-1', 'starter-3', 'starter-4', 'main-2', 'main-3', 'main-4', 'main-5', 'dessert-1', 'dessert-2', 'dessert-3', 'dessert-4'];
      items = items.filter(item => vegDishIds.includes(item.id));
    }
    
    // Apply spicy filter if enabled
    if (spicyOnly) {
      // Mock spicy filter
      const spicyDishIds = ['starter-2', 'starter-5', 'main-1', 'main-6', 'biryani-1', 'biryani-2'];
      items = items.filter(item => spicyDishIds.includes(item.id));
    }
    
    // Apply best sellers filter if enabled
    if (bestSellersOnly) {
      items = items.filter(item => item.isBestSeller === true);
    }

    setFilteredItems(items);
  }, [activeCategory, searchQuery, vegOnly, spicyOnly, bestSellersOnly]);

  const toggleVegFilter = () => {
    setVegOnly(!vegOnly);
    if (!vegOnly) {
      setSpicyOnly(false);
      setBestSellersOnly(false);
    }
  };

  const toggleSpicyFilter = () => {
    setSpicyOnly(!spicyOnly);
    if (!spicyOnly) {
      setVegOnly(false);
      setBestSellersOnly(false);
    }
  };

  const toggleBestSellersFilter = () => {
    setBestSellersOnly(!bestSellersOnly);
    if (!bestSellersOnly) {
      setVegOnly(false);
      setSpicyOnly(false);
    }
  };

  const handleStartOrdering = () => {
    // Scroll to the menu section
    const menuSection = document.getElementById('menu-section');
    if (menuSection) {
      menuSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <Layout title="Food Hall Express" showBackButton={false}>
      {/* Enhanced Hero Banner with Gradient Overlay and Better Typography */}
      <div className="relative overflow-hidden rounded-2xl mb-8 shadow-2xl">
        <div className="absolute inset-0 bg-cover bg-center" 
            style={{ 
              backgroundImage: "url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=2070&auto=format&fit=crop')",
            }}>
        </div>
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/80 via-purple-900/70 to-pink-900/60"></div>
        <div className="relative p-12 md:p-16 text-center">
          {/* Floating decoration elements */}
          <div className="absolute top-8 left-8 w-6 h-6 bg-yellow-400 rounded-full animate-pulse opacity-70"></div>
          <div className="absolute top-12 right-12 w-4 h-4 bg-pink-400 rounded-full animate-pulse opacity-60 animation-delay-300"></div>
          <div className="absolute bottom-8 left-16 w-5 h-5 bg-green-400 rounded-full animate-pulse opacity-50 animation-delay-600"></div>
          
          {/* Enhanced Logo Section */}
          <div className="flex justify-center mb-6">
            <div className="relative bg-gradient-to-br from-orange-400 to-red-500 p-4 rounded-2xl shadow-xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <Sparkles size={32} className="text-white" />
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                <Star size={12} className="text-yellow-800" />
              </div>
            </div>
          </div>
          
          <h1 className="font-bold text-4xl md:text-6xl mb-4 text-white font-playfair tracking-tight">
            Welcome to 
            <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent"> Food Hall</span>
          </h1>
          
          <div className="flex items-center justify-center space-x-6 mb-6">
            <div className="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2">
              <Users size={18} className="text-yellow-300" />
              <span className="text-white font-medium">Table #12</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2">
              <Clock size={18} className="text-green-300" />
              <span className="text-white font-medium">Open Now</span>
            </div>
          </div>
          
          <p className="text-xl md:text-2xl text-white/90 mb-8 font-inter max-w-2xl mx-auto leading-relaxed">
            Experience amazing food with our diverse menu of 
            <span className="text-yellow-300 font-semibold"> fresh, delicious meals </span> 
            crafted with love
          </p>
          
          <Button 
            onClick={handleStartOrdering}
            className="btn-primary text-lg px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
          >
            <Sparkles className="mr-2" size={20} />
            Start Ordering
          </Button>
        </div>
      </div>
      
      {/* Menu Section with ID for smooth scrolling */}
      <div id="menu-section">
        {/* Enhanced Search & Filter Section */}
        <div className="mb-8 bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="relative mb-4">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <Input 
              type="text" 
              placeholder="Search for your favorite dishes..." 
              className="pl-12 pr-4 py-3 text-lg rounded-xl border-2 border-gray-200 focus:border-primary transition-colors"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="flex items-center gap-3 overflow-x-auto pb-2">
            <Toggle 
              variant={vegOnly ? "default" : "outline"} 
              className={`text-sm px-6 py-3 h-auto rounded-full transition-all hover:scale-105 font-medium ${vegOnly ? 'bg-green-500 text-white shadow-lg' : 'bg-green-50 text-green-700 border-green-200'}`}
              pressed={vegOnly}
              onPressedChange={toggleVegFilter}
            >
              🌱 Vegetarian
            </Toggle>
            
            <Toggle 
              variant={spicyOnly ? "default" : "outline"}
              className={`text-sm px-6 py-3 h-auto rounded-full transition-all hover:scale-105 font-medium ${spicyOnly ? 'bg-red-500 text-white shadow-lg' : 'bg-red-50 text-red-700 border-red-200'}`}
              pressed={spicyOnly}
              onPressedChange={toggleSpicyFilter}
            >
              🌶️ Spicy
            </Toggle>
            
            <Toggle 
              variant={bestSellersOnly ? "default" : "outline"}
              className={`text-sm px-6 py-3 h-auto rounded-full transition-all hover:scale-105 font-medium ${bestSellersOnly ? 'bg-yellow-500 text-white shadow-lg' : 'bg-yellow-50 text-yellow-700 border-yellow-200'}`}
              pressed={bestSellersOnly}
              onPressedChange={toggleBestSellersFilter}
            >
              ⭐ Bestsellers
            </Toggle>
          </div>
        </div>
        
        {/* Enhanced Category Tabs */}
        {!searchQuery && (
          <div className="sticky top-16 bg-white/95 backdrop-blur-sm pt-4 pb-4 -mx-4 px-4 z-10 mb-8 border-b border-gray-100">
            <div className="overflow-x-auto">
              <div className="flex space-x-3 pb-2">
                {menuCategories.map((category) => (
                  <button
                    key={category.id}
                    className={`whitespace-nowrap px-6 py-3 rounded-full text-sm font-semibold transition-all hover:scale-105 transform shadow-md ${
                      activeCategory === category.id
                        ? 'bg-gradient-to-r from-primary to-red-500 text-white shadow-lg'
                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                    }`}
                    onClick={() => {
                      setActiveCategory(category.id);
                      setSearchQuery('');
                    }}
                    aria-label={`Show ${category.name}`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Menu Items with Enhanced Layout */}
        {loading ? (
          <div className="space-y-8">
            <CategorySkeleton />
            <CategorySkeleton />
          </div>
        ) : (
          <div>
            {searchQuery ? (
              <div>
                <div className="flex items-center gap-4 mb-8">
                  <h2 className="text-2xl font-bold text-gray-800">Search Results</h2>
                  <div className="h-1 bg-gradient-to-r from-primary to-red-500 flex-grow rounded-full"></div>
                  <span className="text-gray-500 bg-gray-100 px-3 py-1 rounded-full text-sm">
                    {filteredItems.length} dishes found
                  </span>
                </div>
                
                {filteredItems.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredItems.map((item) => (
                      <Card key={item.id} className="menu-card group hover:scale-105 transition-transform duration-300 overflow-hidden border-0 shadow-lg hover:shadow-2xl">
                        <div className="relative">
                          <img src={item.image} alt={item.name} className="menu-image h-48 w-full object-cover" />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                          {item.isBestSeller && (
                            <div className="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                              ⭐ Bestseller
                            </div>
                          )}
                        </div>
                        <div className="p-5 bg-gradient-to-b from-white to-gray-50">
                          <div className="flex justify-between items-start mb-2">
                            <h3 className="font-semibold text-lg text-gray-800">{item.name}</h3>
                            <span className="text-primary font-bold text-lg">₹{item.price}</span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.description}</p>
                          <Button className="w-full bg-gradient-to-r from-primary to-red-500 hover:from-red-500 hover:to-primary text-white rounded-lg py-2 transition-all duration-300 hover:shadow-lg">
                            Add to Cart
                          </Button>
                        </div>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-16 bg-gray-50 rounded-2xl">
                    <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Search size={32} className="text-gray-400" />
                    </div>
                    <p className="text-gray-600 text-lg mb-4">No dishes found matching "{searchQuery}"</p>
                    <Button variant="outline" className="rounded-full px-6" onClick={() => setSearchQuery('')}>
                      Clear Search
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <MenuCategory
                title={menuCategories.find(c => c.id === activeCategory)?.name || ''}
                items={filteredItems}
              />
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default MenuPage;
