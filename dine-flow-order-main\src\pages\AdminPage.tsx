
import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, ChefHat, CheckCircle, Truck, Users, DollarSign, AlertTriangle } from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
}

interface Order {
  id: string;
  tableNumber: number;
  items: OrderItem[];
  total: number;
  status: 'new' | 'preparing' | 'ready' | 'delivered';
  timestamp: Date;
  chef?: string;
  acknowledgedAt?: Date;
  estimatedTime?: number; // in minutes
}

// Available chefs
const availableChefs = [
  'Chef <PERSON><PERSON>',
  'Chef <PERSON><PERSON>',
  'Chef <PERSON>',
  'Chef <PERSON>',
  'Chef <PERSON><PERSON><PERSON>'
];

// Mo<PERSON> orders data - sorted by timestamp (latest first)
const mockOrders: Order[] = [
  {
    id: 'ORD-2024-9876543210',
    tableNumber: 12,
    items: [
      { id: '1', name: 'Chocolate Lava Cake', quantity: 2, price: 299 }
    ],
    total: 598,
    status: 'preparing',
    timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    chef: 'Chef Rajesh',
    acknowledgedAt: new Date(Date.now() - 10 * 60 * 1000),
    estimatedTime: 25
  },
  {
    id: 'ORD-2024-5555666677',
    tableNumber: 8,
    items: [
      { id: '4', name: 'Paneer Tikka', quantity: 1, price: 350 },
      { id: '5', name: 'Garlic Naan', quantity: 3, price: 80 }
    ],
    total: 590,
    status: 'new',
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
  },
  {
    id: 'ORD-2024-1234567890',
    tableNumber: 5,
    items: [
      { id: '2', name: 'Butter Chicken', quantity: 1, price: 450 },
      { id: '3', name: 'Masala Chai', quantity: 2, price: 125 }
    ],
    total: 697,
    status: 'delivered',
    timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    chef: 'Chef Priya'
  }
];

const AdminPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>(mockOrders);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second for real-time display
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Sort orders by timestamp (latest first)
  const sortedOrders = [...orders].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'new': return 'bg-red-100 text-red-800';
      case 'preparing': return 'bg-yellow-100 text-yellow-800';
      case 'ready': return 'bg-green-100 text-green-800';
      case 'delivered': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'new': return <Clock size={16} />;
      case 'preparing': return <ChefHat size={16} />;
      case 'ready': return <CheckCircle size={16} />;
      case 'delivered': return <Truck size={16} />;
      default: return <Clock size={16} />;
    }
  };

  const updateOrderStatus = (orderId: string, newStatus: Order['status'], chef?: string) => {
    setOrders(prevOrders => 
      prevOrders.map(order => 
        order.id === orderId 
          ? { 
              ...order, 
              status: newStatus,
              chef: chef || order.chef,
              acknowledgedAt: newStatus === 'preparing' ? new Date() : order.acknowledgedAt,
              estimatedTime: newStatus === 'preparing' ? 30 : order.estimatedTime
            }
          : order
      )
    );
  };

  const assignChef = (orderId: string, chefName: string) => {
    setOrders(prevOrders => 
      prevOrders.map(order => 
        order.id === orderId 
          ? { 
              ...order, 
              chef: chefName,
              status: 'preparing',
              acknowledgedAt: new Date(),
              estimatedTime: 30
            }
          : order
      )
    );
  };

  const getTimeAgo = (date: Date) => {
    const minutes = Math.floor((currentTime.getTime() - date.getTime()) / (1000 * 60));
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes} minutes ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  };

  const getRemainingTime = (order: Order) => {
    if (!order.acknowledgedAt || !order.estimatedTime) return '--';
    const elapsed = Math.floor((currentTime.getTime() - order.acknowledgedAt.getTime()) / (1000 * 60));
    const remaining = order.estimatedTime - elapsed;
    if (remaining <= 0) return 'Overdue';
    return `${remaining}m ${60 - (currentTime.getSeconds())}s`;
  };

  const getAcknowledgmentStatus = (order: Order) => {
    if (order.status !== 'new') return null;
    const minutesSinceOrder = Math.floor((currentTime.getTime() - order.timestamp.getTime()) / (1000 * 60));
    if (minutesSinceOrder >= 10) {
      return { status: 'urgent', message: 'URGENT: 10+ minutes without acknowledgment!' };
    } else if (minutesSinceOrder >= 5) {
      return { status: 'warning', message: 'Warning: 5+ minutes without acknowledgment' };
    }
    return { status: 'normal', message: `${10 - minutesSinceOrder} minutes left to acknowledge` };
  };

  const orderCounts = {
    new: orders.filter(o => o.status === 'new').length,
    preparing: orders.filter(o => o.status === 'preparing').length,
    ready: orders.filter(o => o.status === 'ready').length,
    delivered: orders.filter(o => o.status === 'delivered').length
  };

  return (
    <Layout title="Admin Dashboard" showBackButton={false} showCart={false}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-primary/10 p-3 rounded-full">
            <ChefHat className="text-primary" size={24} />
          </div>
          <div>
            <h1 className="text-2xl font-bold">🍽️ Food Orders Management</h1>
            <p className="text-gray-600">Real-time order tracking and management</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Clock className="text-red-500" size={20} />
                <span className="font-semibold text-red-500">New</span>
              </div>
              <div className="text-2xl font-bold">{orderCounts.new}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <ChefHat className="text-yellow-500" size={20} />
                <span className="font-semibold text-yellow-500">Preparing</span>
              </div>
              <div className="text-2xl font-bold">{orderCounts.preparing}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <CheckCircle className="text-green-500" size={20} />
                <span className="font-semibold text-green-500">Ready</span>
              </div>
              <div className="text-2xl font-bold">{orderCounts.ready}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Truck className="text-gray-500" size={20} />
                <span className="font-semibold text-gray-500">Delivered</span>
              </div>
              <div className="text-2xl font-bold">{orderCounts.delivered}</div>
            </CardContent>
          </Card>
        </div>

        {/* Orders Table */}
        <Card>
          <CardHeader>
            <CardTitle>📋 Order Management (Latest First)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>📋 Order ID</TableHead>
                    <TableHead>🏠 Table</TableHead>
                    <TableHead>⏰ Time</TableHead>
                    <TableHead>🍽️ Items</TableHead>
                    <TableHead>💰 Total</TableHead>
                    <TableHead>📊 Status</TableHead>
                    <TableHead>👨‍🍳 Chef Assignment</TableHead>
                    <TableHead>⏱️ Time Remaining</TableHead>
                    <TableHead>⚙️ Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedOrders.map((order) => {
                    const acknowledgmentStatus = getAcknowledgmentStatus(order);
                    return (
                      <TableRow key={order.id} className={acknowledgmentStatus?.status === 'urgent' ? 'bg-red-50' : acknowledgmentStatus?.status === 'warning' ? 'bg-yellow-50' : ''}>
                        <TableCell className="font-mono text-sm">{order.id}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Users size={16} />
                            Table {order.tableNumber}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {order.timestamp.toLocaleTimeString('en-US', { 
                                hour: '2-digit', 
                                minute: '2-digit',
                                hour12: true 
                              })}
                            </div>
                            <div className="text-sm text-gray-500">{getTimeAgo(order.timestamp)}</div>
                            {acknowledgmentStatus && (
                              <div className={`text-xs mt-1 flex items-center gap-1 ${
                                acknowledgmentStatus.status === 'urgent' ? 'text-red-600 font-semibold' :
                                acknowledgmentStatus.status === 'warning' ? 'text-orange-600' : 'text-blue-600'
                              }`}>
                                {acknowledgmentStatus.status === 'urgent' && <AlertTriangle size={12} />}
                                {acknowledgmentStatus.message}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {order.items.map(item => (
                              <div key={item.id} className="text-sm">
                                {item.quantity} × {item.name}
                              </div>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell className="font-semibold">{formatPrice(order.total)}</TableCell>
                        <TableCell>
                          <Badge className={`${getStatusColor(order.status)} flex items-center gap-1 w-fit`}>
                            {getStatusIcon(order.status)}
                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                          </Badge>
                          {order.acknowledgedAt && order.status === 'preparing' && (
                            <div className="text-xs text-gray-500 mt-1">
                              Acknowledged {order.acknowledgedAt.toLocaleTimeString('en-US', { 
                                hour: '2-digit', 
                                minute: '2-digit',
                                hour12: true 
                              })}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {order.status === 'new' ? (
                            <Select onValueChange={(chef) => assignChef(order.id, chef)}>
                              <SelectTrigger className="w-40">
                                <SelectValue placeholder="Assign Chef" />
                              </SelectTrigger>
                              <SelectContent>
                                {availableChefs.map(chef => (
                                  <SelectItem key={chef} value={chef}>
                                    {chef}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <div className="text-sm">
                              {order.chef || '--'}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="font-mono text-sm">
                            {getRemainingTime(order)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            {order.status === 'preparing' && (
                              <Button 
                                size="sm" 
                                onClick={() => updateOrderStatus(order.id, 'ready')}
                                className="bg-green-500 hover:bg-green-600"
                              >
                                ✅ Ready
                              </Button>
                            )}
                            {order.status === 'ready' && (
                              <Button 
                                size="sm" 
                                onClick={() => updateOrderStatus(order.id, 'delivered')}
                                className="bg-gray-500 hover:bg-gray-600"
                              >
                                🚚 Delivered
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default AdminPage;
