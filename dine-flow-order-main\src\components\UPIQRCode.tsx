import React, { useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { QrCode, Copy, Download } from 'lucide-react';
import { generateUPIQRData, RESTAURANT_UPI_CONFIG } from '@/lib/upiPayment';
import { toast } from '@/components/ui/use-toast';

interface UPIQRCodeProps {
  amount: number;
  transactionRef: string;
  tableNumber: string;
}

const UPIQRCode: React.FC<UPIQRCodeProps> = ({ amount, transactionRef, tableNumber }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const qrDataRef = useRef<string>('');

  useEffect(() => {
    generateQRCode();
  }, [amount, transactionRef]);

  const generateQRCode = async () => {
    try {
      // Generate UPI payment data
      const paymentDetails = {
        merchantName: RESTAURANT_UPI_CONFIG.merchantName,
        merchantUPI: RESTAURANT_UPI_CONFIG.merchantUPI,
        amount: amount,
        transactionNote: `Food Hall Express - Table ${tableNumber} - Order ${transactionRef}`,
        transactionRef: transactionRef,
        currency: 'INR'
      };

      const qrData = generateUPIQRData(paymentDetails);
      qrDataRef.current = qrData;

      // Generate QR code using a simple QR code library
      // For now, we'll use a QR code API service
      await generateQRCodeImage(qrData);
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    }
  };

  const generateQRCodeImage = async (data: string) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    try {
      // Use QR Server API to generate QR code
      const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(data)}`;
      
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        canvas.width = 200;
        canvas.height = 200;
        ctx.drawImage(img, 0, 0, 200, 200);
      };
      
      img.onerror = () => {
        // Fallback: Draw a simple placeholder
        canvas.width = 200;
        canvas.height = 200;
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, 200, 200);
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('QR Code', 100, 90);
        ctx.fillText('Scan to Pay', 100, 110);
        ctx.fillText(`₹${amount}`, 100, 130);
      };
      
      img.src = qrApiUrl;
    } catch (error) {
      console.error('Failed to load QR code image:', error);
    }
  };

  const copyUPIId = () => {
    navigator.clipboard.writeText(RESTAURANT_UPI_CONFIG.merchantUPI);
    toast({
      title: "Copied!",
      description: "UPI ID copied to clipboard",
      duration: 2000,
    });
  };

  const copyQRData = () => {
    navigator.clipboard.writeText(qrDataRef.current);
    toast({
      title: "Copied!",
      description: "Payment link copied to clipboard",
      duration: 2000,
    });
  };

  const downloadQRCode = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = `payment-qr-${transactionRef}.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-800">
          <QrCode className="h-5 w-5" />
          Scan to Pay with Any UPI App
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center space-y-4">
          {/* QR Code */}
          <div className="bg-white p-4 rounded-lg border-2 border-blue-200">
            <canvas
              ref={canvasRef}
              className="block"
              style={{ maxWidth: '200px', maxHeight: '200px' }}
            />
          </div>

          {/* Payment Details */}
          <div className="w-full space-y-3">
            <div className="flex items-center justify-between p-3 bg-white rounded border">
              <div>
                <p className="text-sm font-medium">Amount to Pay</p>
                <p className="text-xl font-bold text-primary">₹{amount}</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">Pay to</p>
                <p className="text-xs text-gray-600 font-mono">{RESTAURANT_UPI_CONFIG.merchantUPI}</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-white rounded border">
              <div>
                <p className="text-sm font-medium">Transaction Reference</p>
                <p className="text-xs text-gray-600 font-mono">{transactionRef}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={copyQRData}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              className="flex-1"
              onClick={copyUPIId}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy UPI ID
            </Button>
            <Button
              variant="outline"
              className="flex-1"
              onClick={downloadQRCode}
            >
              <Download className="h-4 w-4 mr-2" />
              Download QR
            </Button>
          </div>

          {/* Instructions */}
          <div className="text-center text-sm text-blue-700 bg-white p-3 rounded border">
            <p className="font-medium mb-1">How to pay:</p>
            <ol className="text-left space-y-1">
              <li>1. Open any UPI app (Google Pay, PhonePe, Paytm, etc.)</li>
              <li>2. Scan this QR code OR copy UPI ID</li>
              <li>3. Verify amount: ₹{amount}</li>
              <li>4. Complete payment with your UPI PIN</li>
              <li>5. Return here and click "Payment Completed"</li>
            </ol>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UPIQRCode;
