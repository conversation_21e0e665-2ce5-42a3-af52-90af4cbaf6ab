
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { useCart } from '../context/CartContext';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  UPI_PROVIDERS,
  RESTAURANT_UPI_CONFIG,
  initiateUPIPayment,
  generateTransactionRef,
  formatUPIAmount,
  createPaymentVerificationInstructions
} from '@/lib/upiPayment';
import { ExternalLink, AlertCircle, CheckCircle, Clock, Copy } from 'lucide-react';
import UPIQRCode from '@/components/UPIQRCode';

const CheckoutPage: React.FC = () => {
  const { 
    items, 
    tableNumber, 
    getSubtotal, 
    getTipAmount, 
    getTotal,
    tipPercentage,
    clearCart
  } = useCart();
  
  const [paymentMethod, setPaymentMethod] = useState<string>('cash');
  const [upiOption, setUpiOption] = useState<string>('googlepay');
  const [loading, setLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'waiting' | 'success' | 'failed'>('idle');
  const [transactionRef, setTransactionRef] = useState<string>('');
  const [paymentURL, setPaymentURL] = useState<string>('');
  const [showPaymentInstructions, setShowPaymentInstructions] = useState(false);
  const navigate = useNavigate();

  // Format price to Indian Rupee format
  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;

  const handleSubmitOrder = async () => {
    setLoading(true);
    setPaymentStatus('processing');

    if (paymentMethod === 'upi') {
      await handleUPIPayment();
    } else {
      // Cash payment
      setTimeout(() => {
        clearCart();
        navigate('/success', {
          state: {
            paymentMethod: 'cash',
            transactionRef: generateTransactionRef('CASH')
          }
        });
        setLoading(false);
        setPaymentStatus('success');
      }, 1500);
    }
  };

  const handleUPIPayment = async () => {
    try {
      const selectedProvider = UPI_PROVIDERS.find(provider => provider.id === upiOption);
      if (!selectedProvider) {
        throw new Error('Invalid UPI provider selected');
      }

      // Generate transaction reference
      const txnRef = generateTransactionRef('UPI');
      setTransactionRef(txnRef);

      // Prepare payment details
      const paymentDetails = {
        merchantName: RESTAURANT_UPI_CONFIG.merchantName,
        merchantUPI: RESTAURANT_UPI_CONFIG.merchantUPI,
        amount: getTotal(),
        transactionNote: `Food Hall Express - Table ${tableNumber} - Order ${txnRef}`,
        transactionRef: txnRef,
        currency: 'INR'
      };

      console.log('🔄 Initiating real UPI payment:', paymentDetails);

      // Show payment initiation message
      toast({
        title: "Opening UPI App",
        description: `Redirecting to ${selectedProvider.name} for payment of ₹${getTotal()}`,
        duration: 4000,
      });

      // Initiate UPI payment
      const result = await initiateUPIPayment(paymentDetails, selectedProvider);

      if (result.success) {
        setPaymentURL(result.paymentURL || '');
        setPaymentStatus('waiting');
        setShowPaymentInstructions(true);
        setLoading(false);

        // Show payment instructions
        toast({
          title: "Payment Initiated",
          description: "Complete the payment in your UPI app and return here to confirm",
          duration: 6000,
        });
      } else {
        throw new Error(result.error || 'Failed to initiate UPI payment');
      }
    } catch (error) {
      console.error('UPI payment failed:', error);
      setPaymentStatus('failed');
      setLoading(false);

      toast({
        title: "Payment Failed",
        description: error instanceof Error ? error.message : 'Unable to process UPI payment. Please try again.',
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  const handlePaymentConfirmation = (success: boolean) => {
    if (success) {
      setPaymentStatus('success');
      clearCart();
      navigate('/success', {
        state: {
          paymentMethod: 'upi',
          upiOption: upiOption,
          transactionRef: transactionRef,
          amount: getTotal()
        }
      });
    } else {
      setPaymentStatus('failed');
      toast({
        title: "Payment Failed",
        description: "Payment was not completed. Please try again.",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "Transaction reference copied to clipboard",
      duration: 2000,
    });
  };

  return (
    <Layout title="Checkout" showBackButton={true} showCart={false}>
      <Card className="mb-6 border-primary/20">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                <path d="M3 11a8 8 0 0 1 16 0"></path>
                <path d="M3 11v3a8 8 0 0 0 16 0v-3"></path>
                <path d="M12 22v-9"></path>
                <path d="M8 14v1"></path>
                <path d="M16 14v1"></path>
              </svg>
            </div>
            <div>
              <h3 className="text-sm text-gray-500">Your Table</h3>
              <p className="text-xl font-semibold">Table #{tableNumber}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardContent className="p-4">
          <h3 className="font-medium mb-3">Order Items</h3>
          
          <div className="space-y-3">
            {items.map((item) => (
              <div key={item.id} className="flex justify-between text-sm">
                <span>{item.quantity} × {item.name}</span>
                <span>{formatPrice(item.price * item.quantity)}</span>
              </div>
            ))}
          </div>
          
          <Separator className="my-3" />
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Subtotal</span>
              <span>{formatPrice(getSubtotal())}</span>
            </div>
            
            {tipPercentage > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Tip ({tipPercentage}%)</span>
                <span>{formatPrice(getTipAmount())}</span>
              </div>
            )}
            
            <div className="flex justify-between font-semibold mt-2">
              <span>Total</span>
              <span className="text-primary">{formatPrice(getTotal())}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardContent className="p-4">
          <h3 className="font-medium mb-4">Payment Method</h3>
          
          <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
            <div className="flex items-center space-x-2 mb-4 pb-4 border-b">
              <RadioGroupItem value="upi" id="upi" />
              <Label htmlFor="upi" className="flex-1 cursor-pointer">
                <div className="flex items-center justify-between">
                  <span className="font-medium">UPI Payment</span>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="bg-blue-100 text-blue-600 px-2 py-1 rounded">Pay online</span>
                  </div>
                </div>
              </Label>
            </div>
            
            {paymentMethod === 'upi' && (
              <div className="mb-4 pl-6">
                <div className="grid grid-cols-2 gap-3">
                  {UPI_PROVIDERS.map(provider => (
                    <button
                      key={provider.id}
                      className={`flex items-center gap-2 p-3 rounded-lg border ${
                        upiOption === provider.id
                          ? 'border-primary bg-primary/5'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                      onClick={() => setUpiOption(provider.id)}
                    >
                      <img src={provider.icon} alt={provider.name} className="w-6 h-6" />
                      <span className="text-sm font-medium">{provider.name}</span>
                    </button>
                  ))}
                </div>

                {/* UPI Configuration Info */}
                <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                    <div className="text-xs text-blue-700">
                      <p className="font-medium">Payment will be made to:</p>
                      <p className="mt-1">{RESTAURANT_UPI_CONFIG.merchantName}</p>
                      <p className="font-mono">{RESTAURANT_UPI_CONFIG.merchantUPI}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="cash" id="cash" />
              <Label htmlFor="cash" className="flex-1 cursor-pointer">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Cash on Delivery</span>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded">Pay at table</span>
                  </div>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Payment Status and Instructions */}
      {showPaymentInstructions && paymentStatus === 'waiting' && (
        <Card className="mb-6 border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <Clock className="h-5 w-5" />
              Payment in Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Complete the payment in your UPI app and return here to confirm your order.
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-white rounded border">
                <div>
                  <p className="text-sm font-medium">Transaction Reference</p>
                  <p className="text-xs text-gray-600 font-mono">{transactionRef}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(transactionRef)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-white rounded border">
                <div>
                  <p className="text-sm font-medium">Amount to Pay</p>
                  <p className="text-lg font-bold text-primary">₹{getTotal()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Pay to</p>
                  <p className="text-xs text-gray-600 font-mono">{RESTAURANT_UPI_CONFIG.merchantUPI}</p>
                </div>
              </div>

              {paymentURL && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => window.open(paymentURL, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open UPI App Again
                  </Button>
                </div>
              )}
            </div>

            <div className="flex gap-3 mt-6">
              <Button
                className="flex-1 bg-green-600 hover:bg-green-700"
                onClick={() => handlePaymentConfirmation(true)}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Payment Completed
              </Button>
              <Button
                variant="outline"
                className="flex-1 border-red-200 text-red-600 hover:bg-red-50"
                onClick={() => handlePaymentConfirmation(false)}
              >
                Payment Failed
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* QR Code Payment Option */}
      {showPaymentInstructions && paymentStatus === 'waiting' && (
        <UPIQRCode
          amount={getTotal()}
          transactionRef={transactionRef}
          tableNumber={tableNumber}
        />
      )}

      {/* Payment Failed State */}
      {paymentStatus === 'failed' && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <Alert className="border-red-200">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                Payment failed. Please try again or choose a different payment method.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      <Button
        className="w-full py-6 text-lg bg-primary hover:bg-primary/90"
        disabled={loading || paymentStatus === 'waiting'}
        onClick={handleSubmitOrder}
      >
        {loading ? "Processing..." :
         paymentStatus === 'waiting' ? "Complete Payment Above" :
         "Place Order"}
      </Button>
    </Layout>
  );
};

export default CheckoutPage;
